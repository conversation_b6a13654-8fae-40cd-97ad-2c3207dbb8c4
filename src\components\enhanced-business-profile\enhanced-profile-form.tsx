'use client';

/**
 * Enhanced Business Profile Form Component
 * Comprehensive data collection form for Revo 1.5 framework
 */

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/use-toast';
import type { EnhancedBusinessProfile, ProductService } from '@/lib/types/enhanced-business-profile';

interface EnhancedProfileFormProps {
  initialProfile?: Partial<EnhancedBusinessProfile>;
  onSave: (profile: EnhancedBusinessProfile) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

export function EnhancedProfileForm({ 
  initialProfile, 
  onSave, 
  onCancel, 
  isLoading = false 
}: EnhancedProfileFormProps) {
  const { toast } = useToast();
  const [profile, setProfile] = useState<Partial<EnhancedBusinessProfile>>(
    initialProfile || {
      coreInfo: {
        businessName: '',
        industryCategory: 'Other',
        businessModel: 'B2C',
        primaryLocation: {
          city: '',
          state: '',
          country: ''
        },
        establishmentYear: new Date().getFullYear(),
        businessSize: 'Small (2-10)'
      },
      offerings: [],
      competitiveDifferentiation: {},
      trustIndicators: {
        businessLongevity: 1
      },
      operationalParams: {
        operatingHours: {
          timezone: 'UTC'
        },
        serviceCoverage: {
          geographicBoundaries: [],
          deliveryZones: []
        },
        contactChannels: {}
      },
      contentPreferences: {
        localizationToggle: false,
        contentTone: 'Professional',
        brandVoice: '',
        contentThemes: [],
        visualStyle: ''
      },
      metadata: {
        profileId: `enhanced_${Date.now()}`,
        userId: '',
        createdAt: new Date(),
        updatedAt: new Date(),
        completionScore: 0,
        isActive: true
      }
    }
  );

  const [currentTab, setCurrentTab] = useState('core-info');

  // Calculate completion score
  const calculateCompletionScore = (): number => {
    let score = 0;
    
    // Core info (30 points)
    if (profile.coreInfo?.businessName) score += 5;
    if (profile.coreInfo?.industryCategory) score += 5;
    if (profile.coreInfo?.businessModel) score += 5;
    if (profile.coreInfo?.primaryLocation?.city) score += 5;
    if (profile.coreInfo?.primaryLocation?.country) score += 5;
    if (profile.coreInfo?.establishmentYear) score += 5;

    // Offerings (25 points)
    if (profile.offerings && profile.offerings.length > 0) score += 15;
    if (profile.offerings?.some(o => o.pricingStructure?.basePrice || o.pricingStructure?.hourlyRate)) score += 10;

    // Trust indicators (20 points)
    if (profile.trustIndicators?.businessLongevity) score += 5;
    if (profile.trustIndicators?.customerBase?.customersServed) score += 5;
    if (profile.trustIndicators?.reviewScores?.google) score += 5;
    if (profile.trustIndicators?.professionalCredentials?.certifications?.length) score += 5;

    // Operational params (15 points)
    if (profile.operationalParams?.contactChannels?.phone) score += 5;
    if (profile.operationalParams?.contactChannels?.email) score += 5;
    if (profile.operationalParams?.operatingHours) score += 5;

    // Content preferences (10 points)
    if (profile.contentPreferences?.brandVoice) score += 5;
    if (profile.contentPreferences?.contentThemes?.length) score += 5;

    return Math.min(100, score);
  };

  const completionScore = calculateCompletionScore();

  const updateProfile = (section: keyof EnhancedBusinessProfile, updates: any) => {
    setProfile(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        ...updates
      }
    }));
  };

  const addOffering = () => {
    const newOffering: ProductService = {
      id: `offering_${Date.now()}`,
      name: '',
      description: '',
      pricingStructure: {
        type: 'Fixed',
        currency: 'USD'
      },
      deliveryTimeline: {
        estimatedTime: ''
      },
      fulfillmentMethod: 'In-person',
      targetCustomerProfile: {
        demographics: [],
        painPoints: []
      },
      primaryBenefits: [],
      isActive: true
    };

    setProfile(prev => ({
      ...prev,
      offerings: [...(prev.offerings || []), newOffering]
    }));
  };

  const updateOffering = (index: number, updates: Partial<ProductService>) => {
    setProfile(prev => ({
      ...prev,
      offerings: prev.offerings?.map((offering, i) => 
        i === index ? { ...offering, ...updates } : offering
      ) || []
    }));
  };

  const removeOffering = (index: number) => {
    setProfile(prev => ({
      ...prev,
      offerings: prev.offerings?.filter((_, i) => i !== index) || []
    }));
  };

  const handleSave = async () => {
    try {
      // Validate required fields
      if (!profile.coreInfo?.businessName) {
        toast({
          title: "Validation Error",
          description: "Business name is required",
          variant: "destructive"
        });
        return;
      }

      if (!profile.coreInfo?.primaryLocation?.city) {
        toast({
          title: "Validation Error", 
          description: "Primary location city is required",
          variant: "destructive"
        });
        return;
      }

      if (!profile.coreInfo?.primaryLocation?.country) {
        toast({
          title: "Validation Error",
          description: "Primary location country is required", 
          variant: "destructive"
        });
        return;
      }

      // Update metadata
      const updatedProfile: EnhancedBusinessProfile = {
        ...profile,
        metadata: {
          ...profile.metadata!,
          completionScore: completionScore,
          updatedAt: new Date()
        }
      } as EnhancedBusinessProfile;

      await onSave(updatedProfile);
      
      toast({
        title: "Success",
        description: "Enhanced business profile saved successfully"
      });

    } catch (error) {
      console.error('Error saving profile:', error);
      toast({
        title: "Error",
        description: "Failed to save profile. Please try again.",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header with completion score */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle>Enhanced Business Profile</CardTitle>
              <CardDescription>
                Comprehensive business information for advanced content generation
              </CardDescription>
            </div>
            <div className="text-right">
              <Badge variant={completionScore >= 80 ? 'default' : completionScore >= 50 ? 'secondary' : 'destructive'}>
                {completionScore}% Complete
              </Badge>
              <Progress value={completionScore} className="w-32 mt-2" />
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Form Tabs */}
      <Tabs value={currentTab} onValueChange={setCurrentTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="core-info">Core Info</TabsTrigger>
          <TabsTrigger value="offerings">Products/Services</TabsTrigger>
          <TabsTrigger value="trust">Trust & Authority</TabsTrigger>
          <TabsTrigger value="operations">Operations</TabsTrigger>
          <TabsTrigger value="content">Content Preferences</TabsTrigger>
        </TabsList>

        {/* Core Information Tab */}
        <TabsContent value="core-info">
          <Card>
            <CardHeader>
              <CardTitle>Core Company Information</CardTitle>
              <CardDescription>Basic business details and classification</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="businessName">Business Name *</Label>
                  <Input
                    id="businessName"
                    value={profile.coreInfo?.businessName || ''}
                    onChange={(e) => updateProfile('coreInfo', { businessName: e.target.value })}
                    placeholder="Enter exact legal business name"
                  />
                </div>
                
                <div>
                  <Label htmlFor="industryCategory">Industry Category *</Label>
                  <Select
                    value={profile.coreInfo?.industryCategory || ''}
                    onValueChange={(value) => updateProfile('coreInfo', { industryCategory: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select industry" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Retail">Retail</SelectItem>
                      <SelectItem value="Restaurant">Restaurant</SelectItem>
                      <SelectItem value="Services">Services</SelectItem>
                      <SelectItem value="Healthcare">Healthcare</SelectItem>
                      <SelectItem value="Tech">Technology</SelectItem>
                      <SelectItem value="Manufacturing">Manufacturing</SelectItem>
                      <SelectItem value="Education">Education</SelectItem>
                      <SelectItem value="Finance">Finance</SelectItem>
                      <SelectItem value="Real Estate">Real Estate</SelectItem>
                      <SelectItem value="Other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="businessModel">Business Model</Label>
                  <Select
                    value={profile.coreInfo?.businessModel || ''}
                    onValueChange={(value) => updateProfile('coreInfo', { businessModel: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select business model" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="B2B">B2B (Business to Business)</SelectItem>
                      <SelectItem value="B2C">B2C (Business to Consumer)</SelectItem>
                      <SelectItem value="B2B2C">B2B2C (Business to Business to Consumer)</SelectItem>
                      <SelectItem value="Marketplace">Marketplace</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="businessSize">Business Size</Label>
                  <Select
                    value={profile.coreInfo?.businessSize || ''}
                    onValueChange={(value) => updateProfile('coreInfo', { businessSize: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select business size" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Solo">Solo</SelectItem>
                      <SelectItem value="Small (2-10)">Small (2-10 employees)</SelectItem>
                      <SelectItem value="Medium (11-50)">Medium (11-50 employees)</SelectItem>
                      <SelectItem value="Large (50+)">Large (50+ employees)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="city">Primary Location City *</Label>
                  <Input
                    id="city"
                    value={profile.coreInfo?.primaryLocation?.city || ''}
                    onChange={(e) => updateProfile('coreInfo', { 
                      primaryLocation: { 
                        ...profile.coreInfo?.primaryLocation, 
                        city: e.target.value 
                      } 
                    })}
                    placeholder="e.g., New York"
                  />
                </div>

                <div>
                  <Label htmlFor="state">State/Province</Label>
                  <Input
                    id="state"
                    value={profile.coreInfo?.primaryLocation?.state || ''}
                    onChange={(e) => updateProfile('coreInfo', { 
                      primaryLocation: { 
                        ...profile.coreInfo?.primaryLocation, 
                        state: e.target.value 
                      } 
                    })}
                    placeholder="e.g., NY"
                  />
                </div>

                <div>
                  <Label htmlFor="country">Country *</Label>
                  <Input
                    id="country"
                    value={profile.coreInfo?.primaryLocation?.country || ''}
                    onChange={(e) => updateProfile('coreInfo', { 
                      primaryLocation: { 
                        ...profile.coreInfo?.primaryLocation, 
                        country: e.target.value 
                      } 
                    })}
                    placeholder="e.g., United States"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="establishmentYear">Establishment Year</Label>
                <Input
                  id="establishmentYear"
                  type="number"
                  value={profile.coreInfo?.establishmentYear || ''}
                  onChange={(e) => updateProfile('coreInfo', { establishmentYear: parseInt(e.target.value) })}
                  placeholder="e.g., 2020"
                  min="1900"
                  max={new Date().getFullYear()}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Products/Services Tab */}
        <TabsContent value="offerings">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Products & Services</CardTitle>
                  <CardDescription>Detailed catalog of your offerings</CardDescription>
                </div>
                <Button onClick={addOffering} variant="outline">
                  Add Offering
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {profile.offerings && profile.offerings.length > 0 ? (
                <div className="space-y-4">
                  {profile.offerings.map((offering, index) => (
                    <Card key={offering.id} className="p-4">
                      <div className="flex justify-between items-start mb-4">
                        <h4 className="font-medium">Offering #{index + 1}</h4>
                        <Button 
                          onClick={() => removeOffering(index)} 
                          variant="destructive" 
                          size="sm"
                        >
                          Remove
                        </Button>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label>Name *</Label>
                          <Input
                            value={offering.name}
                            onChange={(e) => updateOffering(index, { name: e.target.value })}
                            placeholder="Product/Service name"
                          />
                        </div>
                        
                        <div>
                          <Label>Fulfillment Method</Label>
                          <Select
                            value={offering.fulfillmentMethod}
                            onValueChange={(value) => updateOffering(index, { fulfillmentMethod: value as any })}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="In-person">In-person</SelectItem>
                              <SelectItem value="Digital delivery">Digital delivery</SelectItem>
                              <SelectItem value="Shipping">Shipping</SelectItem>
                              <SelectItem value="Pickup">Pickup</SelectItem>
                              <SelectItem value="Hybrid">Hybrid</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <div className="mt-4">
                        <Label>Description</Label>
                        <Textarea
                          value={offering.description}
                          onChange={(e) => updateOffering(index, { description: e.target.value })}
                          placeholder="Detailed explanation of what this provides/solves"
                          rows={3}
                        />
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                        <div>
                          <Label>Pricing Type</Label>
                          <Select
                            value={offering.pricingStructure.type}
                            onValueChange={(value) => updateOffering(index, { 
                              pricingStructure: { 
                                ...offering.pricingStructure, 
                                type: value as any 
                              } 
                            })}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="Fixed">Fixed Price</SelectItem>
                              <SelectItem value="Hourly">Hourly Rate</SelectItem>
                              <SelectItem value="Package">Package Deals</SelectItem>
                              <SelectItem value="Tiered">Tiered Pricing</SelectItem>
                              <SelectItem value="Custom">Custom Quote</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div>
                          <Label>Estimated Timeline</Label>
                          <Input
                            value={offering.deliveryTimeline.estimatedTime}
                            onChange={(e) => updateOffering(index, { 
                              deliveryTimeline: { 
                                ...offering.deliveryTimeline, 
                                estimatedTime: e.target.value 
                              } 
                            })}
                            placeholder="e.g., 24 hours, same day, 2-3 weeks"
                          />
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <p>No offerings added yet. Click "Add Offering" to get started.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Trust & Authority Tab */}
        <TabsContent value="trust">
          <Card>
            <CardHeader>
              <CardTitle>Trust & Authority Indicators</CardTitle>
              <CardDescription>Build credibility with verifiable social proof</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="businessLongevity">Years in Operation</Label>
                  <Input
                    id="businessLongevity"
                    type="number"
                    value={profile.trustIndicators?.businessLongevity || ''}
                    onChange={(e) => updateProfile('trustIndicators', { 
                      businessLongevity: parseInt(e.target.value) 
                    })}
                    placeholder="e.g., 5"
                    min="0"
                  />
                </div>

                <div>
                  <Label htmlFor="customersServed">Customers Served</Label>
                  <Input
                    id="customersServed"
                    type="number"
                    value={profile.trustIndicators?.customerBase?.customersServed || ''}
                    onChange={(e) => updateProfile('trustIndicators', { 
                      customerBase: { 
                        ...profile.trustIndicators?.customerBase,
                        customersServed: parseInt(e.target.value) 
                      } 
                    })}
                    placeholder="e.g., 500"
                    min="0"
                  />
                </div>

                <div>
                  <Label htmlFor="googleRating">Google Rating (out of 5)</Label>
                  <Input
                    id="googleRating"
                    type="number"
                    step="0.1"
                    value={profile.trustIndicators?.reviewScores?.google || ''}
                    onChange={(e) => updateProfile('trustIndicators', { 
                      reviewScores: { 
                        ...profile.trustIndicators?.reviewScores,
                        google: parseFloat(e.target.value) 
                      } 
                    })}
                    placeholder="e.g., 4.8"
                    min="0"
                    max="5"
                  />
                </div>

                <div>
                  <Label htmlFor="yelpRating">Yelp Rating (out of 5)</Label>
                  <Input
                    id="yelpRating"
                    type="number"
                    step="0.1"
                    value={profile.trustIndicators?.reviewScores?.yelp || ''}
                    onChange={(e) => updateProfile('trustIndicators', { 
                      reviewScores: { 
                        ...profile.trustIndicators?.reviewScores,
                        yelp: parseFloat(e.target.value) 
                      } 
                    })}
                    placeholder="e.g., 4.5"
                    min="0"
                    max="5"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Operations Tab */}
        <TabsContent value="operations">
          <Card>
            <CardHeader>
              <CardTitle>Operational Parameters</CardTitle>
              <CardDescription>Business logistics and contact information</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="phone">Phone Number</Label>
                  <Input
                    id="phone"
                    value={profile.operationalParams?.contactChannels?.phone || ''}
                    onChange={(e) => updateProfile('operationalParams', { 
                      contactChannels: { 
                        ...profile.operationalParams?.contactChannels,
                        phone: e.target.value 
                      } 
                    })}
                    placeholder="e.g., +****************"
                  />
                </div>

                <div>
                  <Label htmlFor="email">Email Address</Label>
                  <Input
                    id="email"
                    type="email"
                    value={profile.operationalParams?.contactChannels?.email || ''}
                    onChange={(e) => updateProfile('operationalParams', { 
                      contactChannels: { 
                        ...profile.operationalParams?.contactChannels,
                        email: e.target.value 
                      } 
                    })}
                    placeholder="e.g., <EMAIL>"
                  />
                </div>

                <div>
                  <Label htmlFor="website">Website URL</Label>
                  <Input
                    id="website"
                    value={profile.operationalParams?.contactChannels?.website || ''}
                    onChange={(e) => updateProfile('operationalParams', { 
                      contactChannels: { 
                        ...profile.operationalParams?.contactChannels,
                        website: e.target.value 
                      } 
                    })}
                    placeholder="e.g., https://www.business.com"
                  />
                </div>

                <div>
                  <Label htmlFor="timezone">Timezone</Label>
                  <Input
                    id="timezone"
                    value={profile.operationalParams?.operatingHours?.timezone || ''}
                    onChange={(e) => updateProfile('operationalParams', { 
                      operatingHours: { 
                        ...profile.operationalParams?.operatingHours,
                        timezone: e.target.value 
                      } 
                    })}
                    placeholder="e.g., UTC, EST, PST"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="physicalAddress">Physical Address</Label>
                <Textarea
                  id="physicalAddress"
                  value={profile.operationalParams?.contactChannels?.physicalAddress || ''}
                  onChange={(e) => updateProfile('operationalParams', { 
                    contactChannels: { 
                      ...profile.operationalParams?.contactChannels,
                      physicalAddress: e.target.value 
                    } 
                  })}
                  placeholder="Full business address"
                  rows={2}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Content Preferences Tab */}
        <TabsContent value="content">
          <Card>
            <CardHeader>
              <CardTitle>Content Preferences</CardTitle>
              <CardDescription>AI content generation settings and brand voice</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="localizationToggle"
                  checked={profile.contentPreferences?.localizationToggle || false}
                  onCheckedChange={(checked) => updateProfile('contentPreferences', { 
                    localizationToggle: checked 
                  })}
                />
                <Label htmlFor="localizationToggle">Enable Local Language Integration</Label>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="contentTone">Content Tone</Label>
                  <Select
                    value={profile.contentPreferences?.contentTone || ''}
                    onValueChange={(value) => updateProfile('contentPreferences', { contentTone: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select tone" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Professional">Professional</SelectItem>
                      <SelectItem value="Friendly">Friendly</SelectItem>
                      <SelectItem value="Casual">Casual</SelectItem>
                      <SelectItem value="Authoritative">Authoritative</SelectItem>
                      <SelectItem value="Conversational">Conversational</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="primaryColor">Primary Brand Color</Label>
                  <Input
                    id="primaryColor"
                    type="color"
                    value={profile.contentPreferences?.primaryColor || '#000000'}
                    onChange={(e) => updateProfile('contentPreferences', { primaryColor: e.target.value })}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="brandVoice">Brand Voice Description</Label>
                <Textarea
                  id="brandVoice"
                  value={profile.contentPreferences?.brandVoice || ''}
                  onChange={(e) => updateProfile('contentPreferences', { brandVoice: e.target.value })}
                  placeholder="Describe how your brand communicates (e.g., professional yet approachable, technical but accessible)"
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="visualStyle">Visual Style Preferences</Label>
                <Textarea
                  id="visualStyle"
                  value={profile.contentPreferences?.visualStyle || ''}
                  onChange={(e) => updateProfile('contentPreferences', { visualStyle: e.target.value })}
                  placeholder="Describe your preferred visual style, colors, fonts, imagery preferences"
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Action Buttons */}
      <div className="flex justify-end space-x-4">
        <Button variant="outline" onClick={onCancel} disabled={isLoading}>
          Cancel
        </Button>
        <Button onClick={handleSave} disabled={isLoading}>
          {isLoading ? 'Saving...' : 'Save Profile'}
        </Button>
      </div>
    </div>
  );
}
