/**
 * Circuit Breaker Pattern Implementation
 * Prevents cascading failures when external services (like Gemini API) are down
 * Critical for handling 1,000+ users without system collapse
 */

export enum CircuitState {
  CLOSED = 'CLOSED',     // Normal operation
  OPEN = 'OPEN',         // Circuit is open, requests fail fast
  HALF_OPEN = 'HALF_OPEN' // Testing if service is back up
}

export interface CircuitBreakerConfig {
  failureThreshold: number;    // Number of failures before opening circuit
  recoveryTimeout: number;     // Time to wait before trying again (ms)
  monitoringPeriod: number;    // Time window for failure counting (ms)
  successThreshold: number;    // Successes needed to close circuit from half-open
}

export interface CircuitBreakerStats {
  state: CircuitState;
  failureCount: number;
  successCount: number;
  lastFailureTime: number;
  lastSuccessTime: number;
  totalRequests: number;
  totalFailures: number;
  totalSuccesses: number;
}

export class CircuitBreaker {
  private state: CircuitState = CircuitState.CLOSED;
  private failureCount: number = 0;
  private successCount: number = 0;
  private lastFailureTime: number = 0;
  private lastSuccessTime: number = 0;
  private totalRequests: number = 0;
  private totalFailures: number = 0;
  private totalSuccesses: number = 0;
  private nextAttempt: number = 0;

  constructor(
    private name: string,
    private config: CircuitBreakerConfig
  ) {}

  /**
   * Execute a function with circuit breaker protection
   */
  async execute<T>(
    operation: () => Promise<T>,
    fallback: () => T | Promise<T>
  ): Promise<T> {
    this.totalRequests++;

    // Check if circuit is open
    if (this.state === CircuitState.OPEN) {
      if (Date.now() < this.nextAttempt) {
        console.warn(`🔴 [Circuit Breaker: ${this.name}] Circuit OPEN - using fallback`);
        return await fallback();
      } else {
        // Try to transition to half-open
        this.state = CircuitState.HALF_OPEN;
        this.successCount = 0;
        console.log(`🟡 [Circuit Breaker: ${this.name}] Transitioning to HALF_OPEN`);
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      console.warn(`❌ [Circuit Breaker: ${this.name}] Operation failed:`, error.message);
      return await fallback();
    }
  }

  /**
   * Handle successful operation
   */
  private onSuccess(): void {
    this.successCount++;
    this.totalSuccesses++;
    this.lastSuccessTime = Date.now();

    if (this.state === CircuitState.HALF_OPEN) {
      if (this.successCount >= this.config.successThreshold) {
        this.state = CircuitState.CLOSED;
        this.failureCount = 0;
        console.log(`🟢 [Circuit Breaker: ${this.name}] Circuit CLOSED - service recovered`);
      }
    } else if (this.state === CircuitState.CLOSED) {
      // Reset failure count on success in closed state
      this.failureCount = 0;
    }
  }

  /**
   * Handle failed operation
   */
  private onFailure(): void {
    this.failureCount++;
    this.totalFailures++;
    this.lastFailureTime = Date.now();

    if (this.state === CircuitState.CLOSED || this.state === CircuitState.HALF_OPEN) {
      if (this.failureCount >= this.config.failureThreshold) {
        this.state = CircuitState.OPEN;
        this.nextAttempt = Date.now() + this.config.recoveryTimeout;
        console.error(`🔴 [Circuit Breaker: ${this.name}] Circuit OPENED - too many failures (${this.failureCount})`);
      }
    }
  }

  /**
   * Get current circuit breaker statistics
   */
  getStats(): CircuitBreakerStats {
    return {
      state: this.state,
      failureCount: this.failureCount,
      successCount: this.successCount,
      lastFailureTime: this.lastFailureTime,
      lastSuccessTime: this.lastSuccessTime,
      totalRequests: this.totalRequests,
      totalFailures: this.totalFailures,
      totalSuccesses: this.totalSuccesses
    };
  }

  /**
   * Manually reset the circuit breaker
   */
  reset(): void {
    this.state = CircuitState.CLOSED;
    this.failureCount = 0;
    this.successCount = 0;
    this.nextAttempt = 0;
    console.log(`🔄 [Circuit Breaker: ${this.name}] Manually reset`);
  }

  /**
   * Check if circuit is healthy
   */
  isHealthy(): boolean {
    return this.state === CircuitState.CLOSED;
  }
}

/**
 * Circuit Breaker Manager - Manages multiple circuit breakers
 */
export class CircuitBreakerManager {
  private static instance: CircuitBreakerManager;
  private breakers: Map<string, CircuitBreaker> = new Map();

  private constructor() {}

  static getInstance(): CircuitBreakerManager {
    if (!CircuitBreakerManager.instance) {
      CircuitBreakerManager.instance = new CircuitBreakerManager();
    }
    return CircuitBreakerManager.instance;
  }

  /**
   * Get or create a circuit breaker
   */
  getBreaker(name: string, config?: CircuitBreakerConfig): CircuitBreaker {
    if (!this.breakers.has(name)) {
      const defaultConfig: CircuitBreakerConfig = {
        failureThreshold: 5,      // 5 failures
        recoveryTimeout: 60000,   // 1 minute
        monitoringPeriod: 300000, // 5 minutes
        successThreshold: 3       // 3 successes to close
      };

      this.breakers.set(name, new CircuitBreaker(name, config || defaultConfig));
    }

    return this.breakers.get(name)!;
  }

  /**
   * Get all circuit breaker statistics
   */
  getAllStats(): Record<string, CircuitBreakerStats> {
    const stats: Record<string, CircuitBreakerStats> = {};
    
    for (const [name, breaker] of this.breakers) {
      stats[name] = breaker.getStats();
    }

    return stats;
  }

  /**
   * Reset all circuit breakers
   */
  resetAll(): void {
    for (const breaker of this.breakers.values()) {
      breaker.reset();
    }
    console.log('🔄 [Circuit Breaker Manager] All circuits reset');
  }

  /**
   * Get system health overview
   */
  getSystemHealth(): {
    healthy: number;
    total: number;
    unhealthyServices: string[];
    overallHealth: 'HEALTHY' | 'DEGRADED' | 'CRITICAL';
  } {
    const total = this.breakers.size;
    let healthy = 0;
    const unhealthyServices: string[] = [];

    for (const [name, breaker] of this.breakers) {
      if (breaker.isHealthy()) {
        healthy++;
      } else {
        unhealthyServices.push(name);
      }
    }

    let overallHealth: 'HEALTHY' | 'DEGRADED' | 'CRITICAL';
    const healthPercentage = total > 0 ? (healthy / total) * 100 : 100;

    if (healthPercentage >= 80) {
      overallHealth = 'HEALTHY';
    } else if (healthPercentage >= 50) {
      overallHealth = 'DEGRADED';
    } else {
      overallHealth = 'CRITICAL';
    }

    return {
      healthy,
      total,
      unhealthyServices,
      overallHealth
    };
  }
}
