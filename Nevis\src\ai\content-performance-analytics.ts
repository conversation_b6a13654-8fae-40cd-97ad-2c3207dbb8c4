/**
 * Content Performance Analytics - Comprehensive tracking and analysis of content performance
 * Provides detailed insights into what works and what doesn't for content optimization
 */

export interface PerformanceMetrics {
  contentId: string;
  headline: string;
  subheadline: string;
  caption: string;
  cta: string;
  framework: string;
  businessContext: {
    businessName: string;
    businessType: string;
    location: string;
    platform: string;
  };
  metrics: {
    impressions: number;
    clicks: number;
    conversions: number;
    engagement: number;
    shares: number;
    comments: number;
    likes: number;
    saves: number;
    reach: number;
    frequency: number;
  };
  timing: {
    postedAt: number;
    peakEngagementTime: number;
    duration: number;
  };
  demographics: {
    ageGroups: Record<string, number>;
    genders: Record<string, number>;
    locations: Record<string, number>;
    interests: string[];
  };
  performance: {
    ctr: number; // Click-through rate
    ctr: number; // Conversion rate
    engagementRate: number;
    shareRate: number;
    commentRate: number;
    likeRate: number;
    saveRate: number;
    reachRate: number;
    frequencyRate: number;
  };
  quality: {
    uniquenessScore: number;
    creativityScore: number;
    relevanceScore: number;
    emotionalScore: number;
    clarityScore: number;
    overallScore: number;
  };
  timestamp: number;
}

export interface PerformanceInsights {
  topPerformers: Array<{
    contentId: string;
    headline: string;
    performance: number;
    metrics: PerformanceMetrics['metrics'];
    framework: string;
    reasons: string[];
  }>;
  underperformers: Array<{
    contentId: string;
    headline: string;
    performance: number;
    metrics: PerformanceMetrics['metrics'];
    framework: string;
    issues: string[];
  }>;
  trends: {
    performanceOverTime: Array<{
      date: string;
      avgPerformance: number;
      contentCount: number;
    }>;
    frameworkPerformance: Array<{
      framework: string;
      avgPerformance: number;
      usageCount: number;
      trend: 'up' | 'down' | 'stable';
    }>;
    businessTypePerformance: Array<{
      businessType: string;
      avgPerformance: number;
      contentCount: number;
      trend: 'up' | 'down' | 'stable';
    }>;
    locationPerformance: Array<{
      location: string;
      avgPerformance: number;
      contentCount: number;
      trend: 'up' | 'down' | 'stable';
    }>;
    platformPerformance: Array<{
      platform: string;
      avgPerformance: number;
      contentCount: number;
      trend: 'up' | 'down' | 'stable';
    }>;
  };
  patterns: {
    highPerformingPatterns: Array<{
      pattern: string;
      avgPerformance: number;
      frequency: number;
      confidence: number;
    }>;
    lowPerformingPatterns: Array<{
      pattern: string;
      avgPerformance: number;
      frequency: number;
      confidence: number;
    }>;
    optimalTiming: Array<{
      timeSlot: string;
      avgPerformance: number;
      contentCount: number;
    }>;
    optimalLength: Array<{
      lengthRange: string;
      avgPerformance: number;
      contentCount: number;
    }>;
  };
  recommendations: Array<{
    type: 'framework' | 'timing' | 'length' | 'pattern' | 'style' | 'content';
    priority: 'high' | 'medium' | 'low';
    recommendation: string;
    expectedImprovement: number;
    confidence: number;
    reasoning: string[];
  }>;
  benchmarks: {
    industryAverage: number;
    competitorAverage: number;
    personalBest: number;
    improvement: number;
    percentile: number;
  };
}

export interface AnalyticsConfig {
  businessName: string;
  businessType: string;
  location: string;
  platform: string;
  analysisPeriod: number; // days
  minDataPoints: number;
}

export class ContentPerformanceAnalytics {
  private performanceData: PerformanceMetrics[] = [];
  private analyticsCache: Map<string, PerformanceInsights> = new Map();
  private cacheExpiry: Map<string, number> = new Map();
  private readonly CACHE_DURATION = 30 * 60 * 1000; // 30 minutes

  constructor(config: AnalyticsConfig) {
    this.initializeAnalytics(config);
  }

  /**
   * Initialize analytics system
   */
  private initializeAnalytics(config: AnalyticsConfig): void {
    console.log('Content performance analytics initialized');
  }

  /**
   * Record content performance
   */
  recordPerformance(metrics: PerformanceMetrics): void {
    this.performanceData.push(metrics);
    
    // Keep only recent data (last 1000 entries)
    if (this.performanceData.length > 1000) {
      this.performanceData = this.performanceData.slice(-1000);
    }
    
    // Clear cache when new data is added
    this.clearCache();
  }

  /**
   * Get comprehensive performance insights
   */
  getPerformanceInsights(config: AnalyticsConfig): PerformanceInsights {
    const cacheKey = `${config.businessName}_${config.businessType}_${config.location}_${config.platform}`;
    
    // Check cache first
    if (this.isCacheValid(cacheKey)) {
      return this.analyticsCache.get(cacheKey)!;
    }

    try {
      // Filter data by analysis period
      const filteredData = this.filterDataByPeriod(config.analysisPeriod);
      
      if (filteredData.length < config.minDataPoints) {
        return this.getInsufficientDataInsights();
      }

      // Generate insights
      const insights: PerformanceInsights = {
        topPerformers: this.getTopPerformers(filteredData),
        underperformers: this.getUnderperformers(filteredData),
        trends: this.analyzeTrends(filteredData),
        patterns: this.analyzePatterns(filteredData),
        recommendations: this.generateRecommendations(filteredData, config),
        benchmarks: this.calculateBenchmarks(filteredData, config)
      };

      // Cache the result
      this.analyticsCache.set(cacheKey, insights);
      this.cacheExpiry.set(cacheKey, Date.now() + this.CACHE_DURATION);

      return insights;
    } catch (error) {
      console.error('Performance analytics failed:', error);
      return this.getFallbackInsights();
    }
  }

  /**
   * Filter data by analysis period
   */
  private filterDataByPeriod(analysisPeriod: number): PerformanceMetrics[] {
    const cutoffDate = Date.now() - (analysisPeriod * 24 * 60 * 60 * 1000);
    return this.performanceData.filter(data => data.timestamp >= cutoffDate);
  }

  /**
   * Get top performers
   */
  private getTopPerformers(data: PerformanceMetrics[]): PerformanceInsights['topPerformers'] {
    const performers = data.map(item => ({
      contentId: item.contentId,
      headline: item.headline,
      performance: this.calculateOverallPerformance(item),
      metrics: item.metrics,
      framework: item.framework,
      reasons: this.getPerformanceReasons(item)
    }));

    return performers
      .sort((a, b) => b.performance - a.performance)
      .slice(0, 10);
  }

  /**
   * Get underperformers
   */
  private getUnderperformers(data: PerformanceMetrics[]): PerformanceInsights['underperformers'] {
    const performers = data.map(item => ({
      contentId: item.contentId,
      headline: item.headline,
      performance: this.calculateOverallPerformance(item),
      metrics: item.metrics,
      framework: item.framework,
      issues: this.getPerformanceIssues(item)
    }));

    return performers
      .sort((a, b) => a.performance - b.performance)
      .slice(0, 10);
  }

  /**
   * Analyze trends
   */
  private analyzeTrends(data: PerformanceMetrics[]): PerformanceInsights['trends'] {
    return {
      performanceOverTime: this.analyzePerformanceOverTime(data),
      frameworkPerformance: this.analyzeFrameworkPerformance(data),
      businessTypePerformance: this.analyzeBusinessTypePerformance(data),
      locationPerformance: this.analyzeLocationPerformance(data),
      platformPerformance: this.analyzePlatformPerformance(data)
    };
  }

  /**
   * Analyze performance over time
   */
  private analyzePerformanceOverTime(data: PerformanceMetrics[]): Array<{
    date: string;
    avgPerformance: number;
    contentCount: number;
  }> {
    const dailyPerformance: Map<string, { totalPerformance: number; count: number }> = new Map();
    
    data.forEach(item => {
      const date = new Date(item.timestamp).toISOString().split('T')[0];
      const performance = this.calculateOverallPerformance(item);
      
      if (!dailyPerformance.has(date)) {
        dailyPerformance.set(date, { totalPerformance: 0, count: 0 });
      }
      
      const dayData = dailyPerformance.get(date)!;
      dayData.totalPerformance += performance;
      dayData.count += 1;
    });

    return Array.from(dailyPerformance.entries())
      .map(([date, data]) => ({
        date,
        avgPerformance: data.totalPerformance / data.count,
        contentCount: data.count
      }))
      .sort((a, b) => a.date.localeCompare(b.date));
  }

  /**
   * Analyze framework performance
   */
  private analyzeFrameworkPerformance(data: PerformanceMetrics[]): Array<{
    framework: string;
    avgPerformance: number;
    usageCount: number;
    trend: 'up' | 'down' | 'stable';
  }> {
    const frameworkData: Map<string, { totalPerformance: number; count: number; performances: number[] }> = new Map();
    
    data.forEach(item => {
      const framework = item.framework;
      const performance = this.calculateOverallPerformance(item);
      
      if (!frameworkData.has(framework)) {
        frameworkData.set(framework, { totalPerformance: 0, count: 0, performances: [] });
      }
      
      const frameworkInfo = frameworkData.get(framework)!;
      frameworkInfo.totalPerformance += performance;
      frameworkInfo.count += 1;
      frameworkInfo.performances.push(performance);
    });

    return Array.from(frameworkData.entries())
      .map(([framework, data]) => {
        const avgPerformance = data.totalPerformance / data.count;
        const trend = this.calculateTrend(data.performances);
        
        return {
          framework,
          avgPerformance,
          usageCount: data.count,
          trend
        };
      })
      .sort((a, b) => b.avgPerformance - a.avgPerformance);
  }

  /**
   * Analyze business type performance
   */
  private analyzeBusinessTypePerformance(data: PerformanceMetrics[]): Array<{
    businessType: string;
    avgPerformance: number;
    contentCount: number;
    trend: 'up' | 'down' | 'stable';
  }> {
    const businessTypeData: Map<string, { totalPerformance: number; count: number; performances: number[] }> = new Map();
    
    data.forEach(item => {
      const businessType = item.businessContext.businessType;
      const performance = this.calculateOverallPerformance(item);
      
      if (!businessTypeData.has(businessType)) {
        businessTypeData.set(businessType, { totalPerformance: 0, count: 0, performances: [] });
      }
      
      const businessTypeInfo = businessTypeData.get(businessType)!;
      businessTypeInfo.totalPerformance += performance;
      businessTypeInfo.count += 1;
      businessTypeInfo.performances.push(performance);
    });

    return Array.from(businessTypeData.entries())
      .map(([businessType, data]) => {
        const avgPerformance = data.totalPerformance / data.count;
        const trend = this.calculateTrend(data.performances);
        
        return {
          businessType,
          avgPerformance,
          contentCount: data.count,
          trend
        };
      })
      .sort((a, b) => b.avgPerformance - a.avgPerformance);
  }

  /**
   * Analyze location performance
   */
  private analyzeLocationPerformance(data: PerformanceMetrics[]): Array<{
    location: string;
    avgPerformance: number;
    contentCount: number;
    trend: 'up' | 'down' | 'stable';
  }> {
    const locationData: Map<string, { totalPerformance: number; count: number; performances: number[] }> = new Map();
    
    data.forEach(item => {
      const location = item.businessContext.location;
      const performance = this.calculateOverallPerformance(item);
      
      if (!locationData.has(location)) {
        locationData.set(location, { totalPerformance: 0, count: 0, performances: [] });
      }
      
      const locationInfo = locationData.get(location)!;
      locationInfo.totalPerformance += performance;
      locationInfo.count += 1;
      locationInfo.performances.push(performance);
    });

    return Array.from(locationData.entries())
      .map(([location, data]) => {
        const avgPerformance = data.totalPerformance / data.count;
        const trend = this.calculateTrend(data.performances);
        
        return {
          location,
          avgPerformance,
          contentCount: data.count,
          trend
        };
      })
      .sort((a, b) => b.avgPerformance - a.avgPerformance);
  }

  /**
   * Analyze platform performance
   */
  private analyzePlatformPerformance(data: PerformanceMetrics[]): Array<{
    platform: string;
    avgPerformance: number;
    contentCount: number;
    trend: 'up' | 'down' | 'stable';
  }> {
    const platformData: Map<string, { totalPerformance: number; count: number; performances: number[] }> = new Map();
    
    data.forEach(item => {
      const platform = item.businessContext.platform;
      const performance = this.calculateOverallPerformance(item);
      
      if (!platformData.has(platform)) {
        platformData.set(platform, { totalPerformance: 0, count: 0, performances: [] });
      }
      
      const platformInfo = platformData.get(platform)!;
      platformInfo.totalPerformance += performance;
      platformInfo.count += 1;
      platformInfo.performances.push(performance);
    });

    return Array.from(platformData.entries())
      .map(([platform, data]) => {
        const avgPerformance = data.totalPerformance / data.count;
        const trend = this.calculateTrend(data.performances);
        
        return {
          platform,
          avgPerformance,
          contentCount: data.count,
          trend
        };
      })
      .sort((a, b) => b.avgPerformance - a.avgPerformance);
  }

  /**
   * Analyze patterns
   */
  private analyzePatterns(data: PerformanceMetrics[]): PerformanceInsights['patterns'] {
    return {
      highPerformingPatterns: this.getHighPerformingPatterns(data),
      lowPerformingPatterns: this.getLowPerformingPatterns(data),
      optimalTiming: this.getOptimalTiming(data),
      optimalLength: this.getOptimalLength(data)
    };
  }

  /**
   * Get high performing patterns
   */
  private getHighPerformingPatterns(data: PerformanceMetrics[]): Array<{
    pattern: string;
    avgPerformance: number;
    frequency: number;
    confidence: number;
  }> {
    // This would analyze actual patterns in the content
    // For now, return mock data
    return [
      {
        pattern: 'emotional_trigger',
        avgPerformance: 0.85,
        frequency: 0.7,
        confidence: 0.9
      },
      {
        pattern: 'local_reference',
        avgPerformance: 0.82,
        frequency: 0.6,
        confidence: 0.8
      }
    ];
  }

  /**
   * Get low performing patterns
   */
  private getLowPerformingPatterns(data: PerformanceMetrics[]): Array<{
    pattern: string;
    avgPerformance: number;
    frequency: number;
    confidence: number;
  }> {
    // This would analyze actual patterns in the content
    // For now, return mock data
    return [
      {
        pattern: 'generic_phrase',
        avgPerformance: 0.35,
        frequency: 0.4,
        confidence: 0.7
      },
      {
        pattern: 'overused_word',
        avgPerformance: 0.42,
        frequency: 0.5,
        confidence: 0.6
      }
    ];
  }

  /**
   * Get optimal timing
   */
  private getOptimalTiming(data: PerformanceMetrics[]): Array<{
    timeSlot: string;
    avgPerformance: number;
    contentCount: number;
  }> {
    const timeSlots: Map<string, { totalPerformance: number; count: number }> = new Map();
    
    data.forEach(item => {
      const hour = new Date(item.timing.postedAt).getHours();
      let timeSlot: string;
      
      if (hour >= 6 && hour < 12) timeSlot = 'Morning (6-12)';
      else if (hour >= 12 && hour < 18) timeSlot = 'Afternoon (12-18)';
      else if (hour >= 18 && hour < 22) timeSlot = 'Evening (18-22)';
      else timeSlot = 'Night (22-6)';
      
      const performance = this.calculateOverallPerformance(item);
      
      if (!timeSlots.has(timeSlot)) {
        timeSlots.set(timeSlot, { totalPerformance: 0, count: 0 });
      }
      
      const slotData = timeSlots.get(timeSlot)!;
      slotData.totalPerformance += performance;
      slotData.count += 1;
    });

    return Array.from(timeSlots.entries())
      .map(([timeSlot, data]) => ({
        timeSlot,
        avgPerformance: data.totalPerformance / data.count,
        contentCount: data.count
      }))
      .sort((a, b) => b.avgPerformance - a.avgPerformance);
  }

  /**
   * Get optimal length
   */
  private getOptimalLength(data: PerformanceMetrics[]): Array<{
    lengthRange: string;
    avgPerformance: number;
    contentCount: number;
  }> {
    const lengthRanges: Map<string, { totalPerformance: number; count: number }> = new Map();
    
    data.forEach(item => {
      const totalLength = item.headline.length + item.subheadline.length + item.caption.length + item.cta.length;
      let lengthRange: string;
      
      if (totalLength < 100) lengthRange = 'Short (<100)';
      else if (totalLength < 200) lengthRange = 'Medium (100-200)';
      else if (totalLength < 300) lengthRange = 'Long (200-300)';
      else lengthRange = 'Very Long (>300)';
      
      const performance = this.calculateOverallPerformance(item);
      
      if (!lengthRanges.has(lengthRange)) {
        lengthRanges.set(lengthRange, { totalPerformance: 0, count: 0 });
      }
      
      const rangeData = lengthRanges.get(lengthRange)!;
      rangeData.totalPerformance += performance;
      rangeData.count += 1;
    });

    return Array.from(lengthRanges.entries())
      .map(([lengthRange, data]) => ({
        lengthRange,
        avgPerformance: data.totalPerformance / data.count,
        contentCount: data.count
      }))
      .sort((a, b) => b.avgPerformance - a.avgPerformance);
  }

  /**
   * Generate recommendations
   */
  private generateRecommendations(data: PerformanceMetrics[], config: AnalyticsConfig): PerformanceInsights['recommendations'] {
    const recommendations: PerformanceInsights['recommendations'] = [];
    
    // Framework recommendations
    const frameworkPerformance = this.analyzeFrameworkPerformance(data);
    const topFramework = frameworkPerformance[0];
    if (topFramework && topFramework.avgPerformance > 0.7) {
      recommendations.push({
        type: 'framework',
        priority: 'high',
        recommendation: `Use ${topFramework.framework} framework more often (avg performance: ${topFramework.avgPerformance.toFixed(2)})`,
        expectedImprovement: 0.15,
        confidence: 0.8,
        reasoning: ['High performing framework', 'Consistent results', 'Proven track record']
      });
    }
    
    // Timing recommendations
    const optimalTiming = this.getOptimalTiming(data);
    const bestTimeSlot = optimalTiming[0];
    if (bestTimeSlot && bestTimeSlot.avgPerformance > 0.6) {
      recommendations.push({
        type: 'timing',
        priority: 'medium',
        recommendation: `Post content during ${bestTimeSlot.timeSlot} for better performance`,
        expectedImprovement: 0.1,
        confidence: 0.7,
        reasoning: ['Optimal timing identified', 'Higher engagement rates', 'Better reach']
      });
    }
    
    // Length recommendations
    const optimalLength = this.getOptimalLength(data);
    const bestLength = optimalLength[0];
    if (bestLength && bestLength.avgPerformance > 0.6) {
      recommendations.push({
        type: 'length',
        priority: 'medium',
        recommendation: `Optimize content length to ${bestLength.lengthRange} for better performance`,
        expectedImprovement: 0.08,
        confidence: 0.6,
        reasoning: ['Optimal length identified', 'Better readability', 'Improved engagement']
      });
    }
    
    // Pattern recommendations
    const highPerformingPatterns = this.getHighPerformingPatterns(data);
    if (highPerformingPatterns.length > 0) {
      recommendations.push({
        type: 'pattern',
        priority: 'high',
        recommendation: `Focus on ${highPerformingPatterns[0].pattern} pattern for better performance`,
        expectedImprovement: 0.12,
        confidence: 0.8,
        reasoning: ['High performing pattern', 'Proven effectiveness', 'Consistent results']
      });
    }
    
    // Style recommendations
    const avgPerformance = data.reduce((sum, item) => sum + this.calculateOverallPerformance(item), 0) / data.length;
    if (avgPerformance < 0.6) {
      recommendations.push({
        type: 'style',
        priority: 'high',
        recommendation: 'Improve overall content style and emotional appeal',
        expectedImprovement: 0.2,
        confidence: 0.9,
        reasoning: ['Low overall performance', 'Style improvement needed', 'Emotional appeal lacking']
      });
    }
    
    // Content recommendations
    const underperformers = this.getUnderperformers(data);
    if (underperformers.length > 0) {
      recommendations.push({
        type: 'content',
        priority: 'medium',
        recommendation: 'Review and improve underperforming content',
        expectedImprovement: 0.1,
        confidence: 0.7,
        reasoning: ['Underperforming content identified', 'Improvement opportunities', 'Quality enhancement needed']
      });
    }
    
    return recommendations;
  }

  /**
   * Calculate benchmarks
   */
  private calculateBenchmarks(data: PerformanceMetrics[], config: AnalyticsConfig): PerformanceInsights['benchmarks'] {
    const performances = data.map(item => this.calculateOverallPerformance(item));
    const avgPerformance = performances.reduce((sum, p) => sum + p, 0) / performances.length;
    const personalBest = Math.max(...performances);
    
    // Mock industry and competitor averages
    const industryAverage = 0.6;
    const competitorAverage = 0.65;
    
    const improvement = avgPerformance - industryAverage;
    const percentile = this.calculatePercentile(avgPerformance, performances);
    
    return {
      industryAverage,
      competitorAverage,
      personalBest,
      improvement,
      percentile
    };
  }

  /**
   * Calculate overall performance
   */
  private calculateOverallPerformance(item: PerformanceMetrics): number {
    const { metrics, quality } = item;
    
    // Weighted performance calculation
    const performance = (
      metrics.ctr * 0.25 +
      metrics.ctr * 0.25 +
      metrics.engagementRate * 0.2 +
      metrics.shareRate * 0.1 +
      metrics.commentRate * 0.1 +
      metrics.likeRate * 0.05 +
      metrics.saveRate * 0.05
    );
    
    // Quality adjustment
    const qualityAdjustment = quality.overallScore * 0.2;
    
    return Math.max(0, Math.min(1, performance + qualityAdjustment));
  }

  /**
   * Get performance reasons
   */
  private getPerformanceReasons(item: PerformanceMetrics): string[] {
    const reasons: string[] = [];
    
    if (item.metrics.ctr > 0.1) reasons.push('High click-through rate');
    if (item.metrics.ctr > 0.05) reasons.push('High conversion rate');
    if (item.metrics.engagementRate > 0.08) reasons.push('High engagement rate');
    if (item.metrics.shareRate > 0.02) reasons.push('High share rate');
    if (item.quality.overallScore > 0.8) reasons.push('High quality score');
    if (item.quality.creativityScore > 0.8) reasons.push('High creativity score');
    if (item.quality.relevanceScore > 0.8) reasons.push('High relevance score');
    
    return reasons.length > 0 ? reasons : ['Good overall performance'];
  }

  /**
   * Get performance issues
   */
  private getPerformanceIssues(item: PerformanceMetrics): string[] {
    const issues: string[] = [];
    
    if (item.metrics.ctr < 0.02) issues.push('Low click-through rate');
    if (item.metrics.ctr < 0.01) issues.push('Low conversion rate');
    if (item.metrics.engagementRate < 0.03) issues.push('Low engagement rate');
    if (item.metrics.shareRate < 0.005) issues.push('Low share rate');
    if (item.quality.overallScore < 0.5) issues.push('Low quality score');
    if (item.quality.creativityScore < 0.5) issues.push('Low creativity score');
    if (item.quality.relevanceScore < 0.5) issues.push('Low relevance score');
    
    return issues.length > 0 ? issues : ['Performance issues identified'];
  }

  /**
   * Calculate trend
   */
  private calculateTrend(performances: number[]): 'up' | 'down' | 'stable' {
    if (performances.length < 2) return 'stable';
    
    const firstHalf = performances.slice(0, Math.floor(performances.length / 2));
    const secondHalf = performances.slice(Math.floor(performances.length / 2));
    
    const firstAvg = firstHalf.reduce((sum, p) => sum + p, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, p) => sum + p, 0) / secondHalf.length;
    
    const change = secondAvg - firstAvg;
    
    if (change > 0.1) return 'up';
    if (change < -0.1) return 'down';
    return 'stable';
  }

  /**
   * Calculate percentile
   */
  private calculatePercentile(value: number, data: number[]): number {
    const sortedData = data.sort((a, b) => a - b);
    const index = sortedData.findIndex(d => d >= value);
    return index === -1 ? 100 : (index / sortedData.length) * 100;
  }

  /**
   * Cache management
   */
  private isCacheValid(key: string): boolean {
    const expiry = this.cacheExpiry.get(key);
    return expiry ? Date.now() < expiry : false;
  }

  private clearCache(): void {
    this.analyticsCache.clear();
    this.cacheExpiry.clear();
  }

  /**
   * Fallback methods
   */
  private getInsufficientDataInsights(): PerformanceInsights {
    return {
      topPerformers: [],
      underperformers: [],
      trends: {
        performanceOverTime: [],
        frameworkPerformance: [],
        businessTypePerformance: [],
        locationPerformance: [],
        platformPerformance: []
      },
      patterns: {
        highPerformingPatterns: [],
        lowPerformingPatterns: [],
        optimalTiming: [],
        optimalLength: []
      },
      recommendations: [{
        type: 'content',
        priority: 'high',
        recommendation: 'Collect more performance data for better insights',
        expectedImprovement: 0.2,
        confidence: 0.9,
        reasoning: ['Insufficient data', 'Need more content', 'Better analysis possible']
      }],
      benchmarks: {
        industryAverage: 0.6,
        competitorAverage: 0.65,
        personalBest: 0.5,
        improvement: -0.1,
        percentile: 50
      }
    };
  }

  private getFallbackInsights(): PerformanceInsights {
    return {
      topPerformers: [],
      underperformers: [],
      trends: {
        performanceOverTime: [],
        frameworkPerformance: [],
        businessTypePerformance: [],
        locationPerformance: [],
        platformPerformance: []
      },
      patterns: {
        highPerformingPatterns: [],
        lowPerformingPatterns: [],
        optimalTiming: [],
        optimalLength: []
      },
      recommendations: [{
        type: 'content',
        priority: 'high',
        recommendation: 'Analytics system error - retry analysis',
        expectedImprovement: 0.1,
        confidence: 0.5,
        reasoning: ['System error', 'Retry needed', 'Fallback analysis']
      }],
      benchmarks: {
        industryAverage: 0.6,
        competitorAverage: 0.65,
        personalBest: 0.5,
        improvement: -0.1,
        percentile: 50
      }
    };
  }
}

// Export singleton instance
export const contentPerformanceAnalytics = new ContentPerformanceAnalytics({
  businessName: 'default',
  businessType: 'service',
  location: 'default',
  platform: 'social',
  analysisPeriod: 30,
  minDataPoints: 10
});

export default ContentPerformanceAnalytics;

