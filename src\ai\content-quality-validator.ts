/**
 * Content Quality Standards System for Revo 1.5
 * Implements validation, error handling, and completeness checks
 */

import { EnhancedBusinessProfile, Revo15ContentStructure } from '@/lib/types/enhanced-business-profile';

export interface QualityValidationResult {
  overallScore: number; // 0-100
  isValid: boolean;
  specificityScore: number;
  authenticityScore: number;
  relevanceScore: number;
  completenessScore: number;
  toneScore: number;
  problemSolutionScore: number;
  errors: QualityError[];
  warnings: QualityWarning[];
  suggestions: QualitySuggestion[];
}

export interface QualityError {
  type: 'MISSING_CRITICAL_INFO' | 'INVALID_FORMAT' | 'UNVERIFIABLE_CLAIM' | 'IRRELEVANT_CONTENT';
  message: string;
  field?: string;
  severity: 'high' | 'medium' | 'low';
}

export interface QualityWarning {
  type: 'GENERIC_CONTENT' | 'WEAK_CTA' | 'MISSING_SPECIFICS' | 'TONE_MISMATCH';
  message: string;
  suggestion: string;
}

export interface QualitySuggestion {
  type: 'ENHANCE_SPECIFICITY' | 'ADD_CREDIBILITY' | 'IMPROVE_RELEVANCE' | 'STRENGTHEN_CTA';
  message: string;
  example?: string;
}

export class ContentQualityValidator {
  private readonly MINIMUM_QUALITY_SCORE = 70;
  private readonly SPECIFICITY_KEYWORDS = [
    'years', 'customers', 'rating', 'certified', 'hours', 'days', 'weeks',
    'guarantee', 'warranty', '%', 'award', 'licensed', 'experienced'
  ];

  /**
   * Validate content quality against all standards
   */
  validateContent(
    content: Revo15ContentStructure,
    businessProfile: EnhancedBusinessProfile,
    externalData?: {
      rssData?: any;
      trendingData?: any;
      localData?: any;
    }
  ): QualityValidationResult {
    const errors: QualityError[] = [];
    const warnings: QualityWarning[] = [];
    const suggestions: QualitySuggestion[] = [];

    // 1. Specificity Requirement Check
    const specificityScore = this.validateSpecificity(content, businessProfile, errors, warnings, suggestions);

    // 2. Authenticity Check
    const authenticityScore = this.validateAuthenticity(content, businessProfile, errors, warnings);

    // 3. Relevance Filter
    const relevanceScore = this.validateRelevance(content, businessProfile, externalData, warnings, suggestions);

    // 4. Completeness Validation
    const completenessScore = this.validateCompleteness(content, businessProfile, errors, suggestions);

    // 5. Tone Adaptation
    const toneScore = this.validateToneAdaptation(content, businessProfile, warnings, suggestions);

    // 6. Problem-Solution Focus
    const problemSolutionScore = this.validateProblemSolutionFocus(content, businessProfile, warnings, suggestions);

    // Calculate overall score
    const overallScore = Math.round(
      (specificityScore + authenticityScore + relevanceScore + 
       completenessScore + toneScore + problemSolutionScore) / 6
    );

    return {
      overallScore,
      isValid: overallScore >= this.MINIMUM_QUALITY_SCORE && errors.length === 0,
      specificityScore,
      authenticityScore,
      relevanceScore,
      completenessScore,
      toneScore,
      problemSolutionScore,
      errors,
      warnings,
      suggestions
    };
  }

  /**
   * Validate specificity requirement - use concrete numbers, timeframes, measurable benefits
   */
  private validateSpecificity(
    content: Revo15ContentStructure,
    profile: EnhancedBusinessProfile,
    errors: QualityError[],
    warnings: QualityWarning[],
    suggestions: QualitySuggestion[]
  ): number {
    let score = 0;
    const fullContent = `${content.headline} ${content.subheadline} ${content.caption}`.toLowerCase();

    // Check for specific numbers and timeframes
    const hasSpecificNumbers = this.SPECIFICITY_KEYWORDS.some(keyword => 
      fullContent.includes(keyword.toLowerCase())
    );

    if (hasSpecificNumbers) {
      score += 40;
    } else {
      warnings.push({
        type: 'MISSING_SPECIFICS',
        message: 'Content lacks specific numbers, timeframes, or measurable benefits',
        suggestion: 'Add concrete details like "5+ years experience", "24-hour delivery", or "4.8/5 rating"'
      });
    }

    // Check for pricing information
    const hasPricingInfo = profile.offerings.some(offering => 
      offering.pricingStructure.basePrice || offering.pricingStructure.hourlyRate
    );

    if (hasPricingInfo && fullContent.includes('price')) {
      score += 30;
    } else if (hasPricingInfo) {
      suggestions.push({
        type: 'ENHANCE_SPECIFICITY',
        message: 'Consider mentioning specific pricing or value propositions',
        example: 'Starting from $X or "Competitive rates from $X/hour"'
      });
    }

    // Check for delivery timelines
    const hasTimelines = profile.offerings.some(offering => 
      offering.deliveryTimeline.estimatedTime
    );

    if (hasTimelines && (fullContent.includes('hour') || fullContent.includes('day') || fullContent.includes('week'))) {
      score += 30;
    } else if (hasTimelines) {
      suggestions.push({
        type: 'ENHANCE_SPECIFICITY',
        message: 'Highlight specific delivery timelines',
        example: `"${profile.offerings[0]?.deliveryTimeline.estimatedTime} delivery"`
      });
    }

    return Math.min(100, score);
  }

  /**
   * Validate authenticity - only reference verifiable information from business profile
   */
  private validateAuthenticity(
    content: Revo15ContentStructure,
    profile: EnhancedBusinessProfile,
    errors: QualityError[],
    warnings: QualityWarning[]
  ): number {
    let score = 100; // Start with perfect score, deduct for issues
    const fullContent = `${content.headline} ${content.subheadline} ${content.caption}`.toLowerCase();

    // Check for unverifiable claims
    const suspiciousClaims = [
      'best in', 'number one', '#1', 'leading', 'top rated', 'award winning'
    ];

    suspiciousClaims.forEach(claim => {
      if (fullContent.includes(claim.toLowerCase())) {
        // Check if we have evidence for this claim
        const hasEvidence = profile.trustIndicators?.recognition?.awards?.length ||
                           profile.trustIndicators?.reviewScores?.google ||
                           profile.trustIndicators?.recognition?.industryRankings?.length;

        if (!hasEvidence) {
          score -= 20;
          errors.push({
            type: 'UNVERIFIABLE_CLAIM',
            message: `Claim "${claim}" cannot be verified from business profile data`,
            severity: 'high'
          });
        }
      }
    });

    // Verify contact information accuracy
    if (content.caption.includes('phone') || content.callToAction?.includes('call')) {
      if (!profile.operationalParams.contactChannels?.phone) {
        score -= 15;
        errors.push({
          type: 'MISSING_CRITICAL_INFO',
          message: 'Content references phone contact but no phone number provided in profile',
          field: 'contactChannels.phone',
          severity: 'medium'
        });
      }
    }

    // Verify business hours claims
    if (fullContent.includes('24/7') || fullContent.includes('24 hours')) {
      const hasExtendedHours = profile.operationalParams.operatingHours &&
        Object.values(profile.operationalParams.operatingHours).some(hours => 
          typeof hours === 'string' && hours.includes('24')
        );

      if (!hasExtendedHours) {
        score -= 10;
        warnings.push({
          type: 'UNVERIFIABLE_CLAIM',
          message: 'Content claims 24/7 availability but business hours don\'t support this',
          suggestion: 'Update business hours or modify content to reflect actual availability'
        });
      }
    }

    return Math.max(0, score);
  }

  /**
   * Validate relevance - ensure RSS/trend data directly relates to business value proposition
   */
  private validateRelevance(
    content: Revo15ContentStructure,
    profile: EnhancedBusinessProfile,
    externalData: any,
    warnings: QualityWarning[],
    suggestions: QualitySuggestion[]
  ): number {
    let score = 80; // Base score

    // Check if trending topics are relevant to business
    if (externalData?.trendingData?.keywords) {
      const businessKeywords = [
        profile.coreInfo.industryCategory.toLowerCase(),
        ...profile.offerings.map(o => o.name.toLowerCase()),
        profile.coreInfo.primaryLocation.city.toLowerCase()
      ];

      const relevantTrends = externalData.trendingData.keywords.filter((keyword: string) =>
        businessKeywords.some(bk => keyword.toLowerCase().includes(bk) || bk.includes(keyword.toLowerCase()))
      );

      if (relevantTrends.length > 0) {
        score += 20;
      } else {
        warnings.push({
          type: 'IRRELEVANT_CONTENT',
          message: 'Trending topics may not be directly relevant to business',
          suggestion: 'Focus on industry-specific trends or local market developments'
        });
      }
    }

    // Check RSS data relevance
    if (externalData?.rssData?.articles) {
      const businessRelevantArticles = externalData.rssData.articles.filter((article: any) =>
        article.category?.toLowerCase().includes(profile.coreInfo.industryCategory.toLowerCase()) ||
        article.title?.toLowerCase().includes(profile.coreInfo.industryCategory.toLowerCase())
      );

      if (businessRelevantArticles.length === 0) {
        warnings.push({
          type: 'IRRELEVANT_CONTENT',
          message: 'RSS feed data may not be relevant to business industry',
          suggestion: 'Prioritize business profile information over generic news'
        });
      }
    }

    return Math.min(100, score);
  }

  /**
   * Validate completeness - check if critical profile information is present
   */
  private validateCompleteness(
    content: Revo15ContentStructure,
    profile: EnhancedBusinessProfile,
    errors: QualityError[],
    suggestions: QualitySuggestion[]
  ): number {
    let score = 0;

    // Check headline completeness (max 6 words)
    if (content.headline) {
      const wordCount = content.headline.split(' ').length;
      if (wordCount <= 6) {
        score += 20;
      } else {
        errors.push({
          type: 'INVALID_FORMAT',
          message: `Headline exceeds 6 words (${wordCount} words)`,
          field: 'headline',
          severity: 'medium'
        });
      }
    } else {
      errors.push({
        type: 'MISSING_CRITICAL_INFO',
        message: 'Headline is required',
        field: 'headline',
        severity: 'high'
      });
    }

    // Check subheadline completeness (max 25 words)
    if (content.subheadline) {
      const wordCount = content.subheadline.split(' ').length;
      if (wordCount <= 25) {
        score += 20;
      } else {
        errors.push({
          type: 'INVALID_FORMAT',
          message: `Subheadline exceeds 25 words (${wordCount} words)`,
          field: 'subheadline',
          severity: 'medium'
        });
      }
    }

    // Check caption presence and quality
    if (content.caption && content.caption.length > 50) {
      score += 25;
    } else {
      suggestions.push({
        type: 'ENHANCE_SPECIFICITY',
        message: 'Caption should be comprehensive and detailed',
        example: 'Include business strengths, specific services, and contact information'
      });
    }

    // Check CTA presence
    if (content.callToAction) {
      score += 15;
    } else {
      suggestions.push({
        type: 'STRENGTHEN_CTA',
        message: 'Add a clear call-to-action',
        example: 'Call now, Visit website, Book consultation'
      });
    }

    // Check hashtags
    if (content.hashtags && content.hashtags.length >= 5) {
      score += 20;
    } else {
      suggestions.push({
        type: 'ENHANCE_SPECIFICITY',
        message: 'Include at least 5 relevant hashtags',
        example: 'Business name, location, services, industry terms'
      });
    }

    return score;
  }

  /**
   * Validate tone adaptation - match communication style to industry norms
   */
  private validateToneAdaptation(
    content: Revo15ContentStructure,
    profile: EnhancedBusinessProfile,
    warnings: QualityWarning[],
    suggestions: QualitySuggestion[]
  ): number {
    let score = 80; // Base score

    const fullContent = `${content.headline} ${content.subheadline} ${content.caption}`.toLowerCase();
    const industry = profile.coreInfo.industryCategory.toLowerCase();
    const preferredTone = profile.contentPreferences.contentTone.toLowerCase();

    // Industry-specific tone validation
    const industryToneRules = {
      'healthcare': ['professional', 'caring', 'trustworthy', 'reliable'],
      'finance': ['professional', 'secure', 'trustworthy', 'expert'],
      'tech': ['innovative', 'cutting-edge', 'efficient', 'modern'],
      'restaurant': ['appetizing', 'welcoming', 'fresh', 'delicious'],
      'retail': ['trendy', 'affordable', 'quality', 'stylish'],
      'services': ['reliable', 'professional', 'experienced', 'quality']
    };

    const expectedTones = industryToneRules[industry as keyof typeof industryToneRules] || ['professional'];
    
    const hasToneMatch = expectedTones.some(tone => 
      fullContent.includes(tone) || preferredTone.includes(tone)
    );

    if (!hasToneMatch) {
      score -= 20;
      suggestions.push({
        type: 'IMPROVE_RELEVANCE',
        message: `Consider incorporating ${industry}-appropriate tone`,
        example: `Use words like: ${expectedTones.join(', ')}`
      });
    }

    return score;
  }

  /**
   * Validate problem-solution focus - lead with customer pain points
   */
  private validateProblemSolutionFocus(
    content: Revo15ContentStructure,
    profile: EnhancedBusinessProfile,
    warnings: QualityWarning[],
    suggestions: QualitySuggestion[]
  ): number {
    let score = 60; // Base score

    const fullContent = `${content.headline} ${content.subheadline} ${content.caption}`.toLowerCase();

    // Check for problem-solving language
    const problemSolvingKeywords = [
      'solve', 'fix', 'help', 'solution', 'problem', 'challenge', 'issue',
      'need', 'struggle', 'difficulty', 'overcome', 'resolve', 'address'
    ];

    const hasProblemFocus = problemSolvingKeywords.some(keyword => 
      fullContent.includes(keyword)
    );

    if (hasProblemFocus) {
      score += 40;
    } else {
      suggestions.push({
        type: 'IMPROVE_RELEVANCE',
        message: 'Consider leading with customer pain points or problems you solve',
        example: 'Start with challenges your target audience faces'
      });
    }

    // Check if customer pain points from profile are addressed
    const hasCustomerPainPoints = profile.offerings.some(offering => 
      offering.targetCustomerProfile.painPoints.length > 0
    );

    if (hasCustomerPainPoints) {
      const painPointsAddressed = profile.offerings.some(offering =>
        offering.targetCustomerProfile.painPoints.some(painPoint =>
          fullContent.includes(painPoint.toLowerCase())
        )
      );

      if (painPointsAddressed) {
        score += 20;
      } else {
        suggestions.push({
          type: 'IMPROVE_RELEVANCE',
          message: 'Address specific customer pain points from your profile',
          example: `Consider mentioning: ${profile.offerings[0]?.targetCustomerProfile.painPoints[0]}`
        });
      }
    }

    return Math.min(100, score);
  }

  /**
   * Generate improvement recommendations
   */
  generateImprovementPlan(validationResult: QualityValidationResult): {
    priority: 'high' | 'medium' | 'low';
    actions: string[];
    estimatedImpact: number;
  } {
    const actions: string[] = [];
    let estimatedImpact = 0;

    // High priority: Fix errors first
    validationResult.errors.forEach(error => {
      if (error.severity === 'high') {
        actions.push(`CRITICAL: ${error.message}`);
        estimatedImpact += 15;
      }
    });

    // Medium priority: Address major warnings
    validationResult.warnings.forEach(warning => {
      actions.push(`IMPROVE: ${warning.message} - ${warning.suggestion}`);
      estimatedImpact += 8;
    });

    // Low priority: Implement suggestions
    validationResult.suggestions.slice(0, 3).forEach(suggestion => {
      actions.push(`ENHANCE: ${suggestion.message}`);
      estimatedImpact += 5;
    });

    const priority = validationResult.errors.length > 0 ? 'high' : 
                    validationResult.warnings.length > 2 ? 'medium' : 'low';

    return {
      priority,
      actions,
      estimatedImpact: Math.min(100, estimatedImpact)
    };
  }
}

// Export singleton instance
export const contentQualityValidator = new ContentQualityValidator();
