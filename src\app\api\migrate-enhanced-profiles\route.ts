import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

// GET /api/migrate-enhanced-profiles - Check database status
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 [Migration API] Checking enhanced profiles table status...');

    // Use service role key for admin operations
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Check if table exists and is accessible
    const { data, error } = await supabase
      .from('enhanced_business_profiles')
      .select('id')
      .limit(1);

    if (error) {
      if (error.code === '42P01') {
        // Table doesn't exist
        return NextResponse.json({
          success: false,
          tableExists: false,
          error: 'Enhanced profiles table does not exist',
          needsMigration: true,
          message: 'Database migration required to enable enhanced framework'
        });
      } else if (error.code === '42501' || error.message?.includes('RLS') || error.message?.includes('policy')) {
        // RLS policy issue
        return NextResponse.json({
          success: false,
          tableExists: true,
          error: 'Table exists but RLS policies need configuration',
          needsRLSSetup: true,
          message: 'Row Level Security policies need to be configured'
        });
      } else {
        // Other error
        return NextResponse.json({
          success: false,
          tableExists: 'unknown',
          error: 'Database access error',
          details: error.message
        });
      }
    }

    // Table exists and is accessible
    return NextResponse.json({
      success: true,
      tableExists: true,
      message: 'Enhanced profiles table is ready',
      recordCount: data?.length || 0
    });

  } catch (error) {
    console.error('❌ [Migration API] Error checking database status:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to check database status',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// POST /api/migrate-enhanced-profiles - Run migration
export async function POST(request: NextRequest) {
  try {
    console.log('🔄 [Migration API] Starting enhanced profiles table migration...');

    // Use service role key for admin operations
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Create the enhanced_business_profiles table
    const createTableSQL = `
      -- Enhanced Business Profiles Table
      CREATE TABLE IF NOT EXISTS enhanced_business_profiles (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        
        -- Core company information
        core_info JSONB NOT NULL DEFAULT '{}'::jsonb,
        
        -- Products/Services catalog
        offerings JSONB NOT NULL DEFAULT '[]'::jsonb,
        
        -- Competitive differentiation matrix
        competitive_differentiation JSONB NOT NULL DEFAULT '{}'::jsonb,
        
        -- Trust & authority indicators
        trust_indicators JSONB NOT NULL DEFAULT '{}'::jsonb,
        
        -- Operational parameters
        operational_params JSONB NOT NULL DEFAULT '{}'::jsonb,
        
        -- Content preferences
        content_preferences JSONB NOT NULL DEFAULT '{}'::jsonb,
        
        -- System metadata
        metadata JSONB NOT NULL DEFAULT '{}'::jsonb,
        
        -- Timestamps
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `;

    // Create indexes and RLS policies
    const indexAndPolicySQL = `
      -- Create indexes
      CREATE INDEX IF NOT EXISTS idx_enhanced_profiles_user_id ON enhanced_business_profiles(user_id);
      CREATE INDEX IF NOT EXISTS idx_enhanced_profiles_active ON enhanced_business_profiles((metadata->>'isActive'));
      CREATE INDEX IF NOT EXISTS idx_enhanced_profiles_business_name ON enhanced_business_profiles((core_info->>'businessName'));
      CREATE INDEX IF NOT EXISTS idx_enhanced_profiles_industry ON enhanced_business_profiles((core_info->>'industryCategory'));
      CREATE INDEX IF NOT EXISTS idx_enhanced_profiles_location ON enhanced_business_profiles((core_info->'primaryLocation'->>'city'));
      CREATE INDEX IF NOT EXISTS idx_enhanced_profiles_completion ON enhanced_business_profiles(((metadata->>'completionScore')::integer));
      
      -- Enable RLS
      ALTER TABLE enhanced_business_profiles ENABLE ROW LEVEL SECURITY;
      
      -- Create RLS policies
      CREATE POLICY IF NOT EXISTS "Users can view their own enhanced profiles" 
        ON enhanced_business_profiles FOR SELECT 
        USING (auth.uid()::text = user_id);
      
      CREATE POLICY IF NOT EXISTS "Users can insert their own enhanced profiles" 
        ON enhanced_business_profiles FOR INSERT 
        WITH CHECK (auth.uid()::text = user_id);
      
      CREATE POLICY IF NOT EXISTS "Users can update their own enhanced profiles" 
        ON enhanced_business_profiles FOR UPDATE 
        USING (auth.uid()::text = user_id);
      
      CREATE POLICY IF NOT EXISTS "Users can delete their own enhanced profiles" 
        ON enhanced_business_profiles FOR DELETE 
        USING (auth.uid()::text = user_id);
    `;

    // Try to create table using direct SQL execution
    let tableCreated = false;
    try {
      // First, try to check if table exists
      const { error: checkError } = await supabase
        .from('enhanced_business_profiles')
        .select('id')
        .limit(1);
      
      if (checkError && checkError.code === '42P01') {
        // Table doesn't exist, return instructions for manual creation
        console.log('❌ [Migration API] Table does not exist and cannot be created automatically');
        return NextResponse.json({
          success: false,
          error: 'Enhanced profiles table does not exist. Please create it manually in Supabase dashboard.',
          needsManualSetup: true,
          instructions: [
            '1. Go to your Supabase dashboard',
            '2. Navigate to the SQL Editor',
            '3. Run the provided SQL script',
            '4. Come back and click "Check Database Status" to verify'
          ],
          sql: createTableSQL + '\n\n' + indexAndPolicySQL
        }, { status: 503 });
      } else {
        // Table exists or other error
        tableCreated = true;
        console.log('✅ [Migration API] Table already exists or is accessible');
      }
    } catch (error) {
      console.error('❌ [Migration API] Error checking table:', error);
      return NextResponse.json({
        success: false,
        error: 'Database connection error',
        details: error.message
      }, { status: 500 });
    }

    // If we reach here, table exists - test access
    const { data: testData, error: testError } = await supabase
      .from('enhanced_business_profiles')
      .select('id')
      .limit(1);

    if (testError) {
      console.error('❌ [Migration API] Table access test failed:', testError);
      
      // Check if it's an RLS policy issue
      if (testError.code === '42501' || testError.message?.includes('RLS') || testError.message?.includes('policy')) {
        return NextResponse.json({
          success: false,
          error: 'Table exists but RLS policies need to be configured.',
          needsRLSSetup: true,
          instructions: [
            '1. Go to your Supabase dashboard',
            '2. Navigate to Authentication > Policies',
            '3. Find the enhanced_business_profiles table',
            '4. Add the following policies:',
            '   - SELECT: auth.uid()::text = user_id',
            '   - INSERT: auth.uid()::text = user_id',
            '   - UPDATE: auth.uid()::text = user_id',
            '   - DELETE: auth.uid()::text = user_id'
          ],
          sql: indexAndPolicySQL
        }, { status: 503 });
      }
      
      return NextResponse.json({
        success: false,
        error: 'Table access failed',
        details: testError.message
      }, { status: 500 });
    }

    console.log('✅ [Migration API] Enhanced profiles table is ready!');
    return NextResponse.json({
      success: true,
      message: 'Enhanced profiles table is ready for use',
      tableExists: true,
      recordCount: testData?.length || 0
    });

  } catch (error) {
    console.error('❌ [Migration API] Migration failed:', error);
    return NextResponse.json({
      success: false,
      error: 'Migration failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
