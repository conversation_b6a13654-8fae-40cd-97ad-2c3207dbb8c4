/**
 * Enhanced Business Profiles API
 * Handles comprehensive business profile data for Revo 1.5 framework
 */

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Server-side Supabase client with service role key
const supabaseAuth = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// Inline auth verification function
async function verifySupabaseAuth(authHeader: string | null) {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return {
      error: 'Authorization token required',
      status: 401
    };
  }

  const token = authHeader.substring(7); // Remove 'Bearer ' prefix

  try {
    // Verify the token using Supabase
    const { data: { user }, error } = await supabaseAuth.auth.getUser(token);

    if (error || !user) {
      return {
        error: 'Invalid or expired token',
        status: 401
      };
    }

    return {
      user,
      userId: user.id
    };
  } catch (error) {
    console.error('❌ [Auth] Token verification failed:', error);
    return {
      error: 'Token verification failed',
      status: 401
    };
  }
}

// GET /api/enhanced-business-profiles - Load user's enhanced business profiles
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 [Enhanced Profiles API] Loading enhanced business profiles...');

    // Verify authentication
    const authResult = await verifySupabaseAuth(request.headers.get('authorization'));
    if (authResult.error) {
      return NextResponse.json({
        success: false,
        error: authResult.error
      }, { status: authResult.status });
    }

    // Create Supabase client for database operations
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Query enhanced business profiles for the user
    const { data: profiles, error } = await supabase
      .from('enhanced_business_profiles')
      .select('*')
      .eq('user_id', authResult.userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('❌ [Enhanced Profiles API] Database query failed:', error);
      return NextResponse.json({
        success: false,
        error: 'Failed to load enhanced business profiles',
        details: error.message
      }, { status: 500 });
    }

    console.log(`✅ [Enhanced Profiles API] Found ${profiles?.length || 0} enhanced profiles`);

    return NextResponse.json({
      success: true,
      profiles: profiles || [],
      count: profiles?.length || 0,
      message: 'Enhanced business profiles loaded successfully'
    });

  } catch (error) {
    console.error('❌ [Enhanced Profiles API] Error loading profiles:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to load enhanced business profiles',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// POST /api/enhanced-business-profiles - Create new enhanced business profile
export async function POST(request: NextRequest) {
  // Temporarily disabled enhanced framework to resolve server errors
  return NextResponse.json({
    success: false,
    error: 'Enhanced business profiles temporarily disabled for system stability',
    needsMigration: true,
    migrationEndpoint: '/api/migrate-enhanced-profiles'
  }, { status: 503 });
}

// PUT /api/enhanced-business-profiles - Update existing enhanced business profile
export async function PUT(request: NextRequest) {
  // Temporarily disabled enhanced framework to resolve server errors
  return NextResponse.json({
    success: false,
    error: 'Enhanced business profiles temporarily disabled for system stability',
    needsMigration: true,
    migrationEndpoint: '/api/migrate-enhanced-profiles'
  }, { status: 503 });
}

// DELETE /api/enhanced-business-profiles - Delete enhanced business profile
export async function DELETE(request: NextRequest) {
  // Temporarily disabled enhanced framework to resolve server errors
  return NextResponse.json({
    success: false,
    error: 'Enhanced business profiles temporarily disabled for system stability',
    needsMigration: true,
    migrationEndpoint: '/api/migrate-enhanced-profiles'
  }, { status: 503 });
}
