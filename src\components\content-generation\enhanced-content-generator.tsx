'use client';

/**
 * Enhanced Content Generator Component
 * Integrates with the Revo 1.5 enhanced business profile framework
 */

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Copy, Sparkles, AlertTriangle, CheckCircle, Globe, Zap } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useEnhancedBrand } from '@/contexts/enhanced-brand-context';
import type { EnhancedBusinessProfile } from '@/lib/types/enhanced-business-profile';

interface EnhancedContentGeneratorProps {
  onContentGenerated?: (content: any) => void;
  defaultPlatform?: string;
  showProfileSelector?: boolean;
}

interface GeneratedContent {
  headline: string;
  subheadline?: string;
  caption: string;
  callToAction?: string;
  hashtags: string[];
  qualityScore: number;
  improvementSuggestions: string[];
  localizationApplied: boolean;
  dataSourcesUsed: string[];
}

export function EnhancedContentGenerator({ 
  onContentGenerated, 
  defaultPlatform = 'instagram',
  showProfileSelector = true 
}: EnhancedContentGeneratorProps) {
  const { toast } = useToast();
  const { 
    enhancedProfiles, 
    currentEnhancedProfile, 
    selectEnhancedProfile,
    getProfileCompletionScore 
  } = useEnhancedBrand();

  const [selectedProfile, setSelectedProfile] = useState<EnhancedBusinessProfile | null>(
    currentEnhancedProfile
  );
  const [platform, setPlatform] = useState(defaultPlatform);
  const [contentType, setContentType] = useState('promotional');
  const [customPrompt, setCustomPrompt] = useState('');
  const [useLocalization, setUseLocalization] = useState(true);
  const [generating, setGenerating] = useState(false);
  const [generatedContent, setGeneratedContent] = useState<GeneratedContent | null>(null);

  const platforms = [
    { value: 'instagram', label: 'Instagram', icon: '📸' },
    { value: 'facebook', label: 'Facebook', icon: '👥' },
    { value: 'twitter', label: 'Twitter/X', icon: '🐦' },
    { value: 'linkedin', label: 'LinkedIn', icon: '💼' },
    { value: 'tiktok', label: 'TikTok', icon: '🎵' }
  ];

  const contentTypes = [
    { value: 'promotional', label: 'Promotional Post' },
    { value: 'educational', label: 'Educational Content' },
    { value: 'behind-the-scenes', label: 'Behind the Scenes' },
    { value: 'customer-story', label: 'Customer Story' },
    { value: 'product-showcase', label: 'Product Showcase' },
    { value: 'seasonal', label: 'Seasonal/Trending' }
  ];

  const handleProfileSelect = (profileId: string) => {
    const profile = enhancedProfiles.find(p => p.metadata.profileId === profileId);
    if (profile) {
      setSelectedProfile(profile);
      selectEnhancedProfile(profile);
    }
  };

  const handleGenerateContent = async () => {
    if (!selectedProfile) {
      toast({
        title: "Profile Required",
        description: "Please select an enhanced business profile to generate content.",
        variant: "destructive"
      });
      return;
    }

    const completionScore = getProfileCompletionScore(selectedProfile);
    if (completionScore < 50) {
      toast({
        title: "Profile Incomplete",
        description: "Your profile needs to be at least 50% complete for enhanced content generation.",
        variant: "destructive"
      });
      return;
    }

    setGenerating(true);

    try {
      console.log('🎯 [Enhanced Content Generator] Generating content for:', selectedProfile.coreInfo.businessName);

      const response = await fetch('/api/enhanced-content-generation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          profileId: selectedProfile.metadata.profileId,
          platform,
          contentType,
          customPrompt: customPrompt.trim() || undefined,
          useLocalization: useLocalization && selectedProfile.contentPreferences.localizationToggle
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to generate content');
      }

      const result = await response.json();
      setGeneratedContent(result.content);
      
      toast({
        title: "Content Generated!",
        description: `High-quality content created with ${result.content.qualityScore}% quality score.`
      });

      onContentGenerated?.(result.content);

    } catch (error) {
      console.error('❌ [Enhanced Content Generator] Error:', error);
      toast({
        title: "Generation Failed",
        description: error instanceof Error ? error.message : "Failed to generate content. Please try again.",
        variant: "destructive"
      });
    } finally {
      setGenerating(false);
    }
  };

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied!",
      description: `${label} copied to clipboard.`
    });
  };

  const getQualityBadgeColor = (score: number) => {
    if (score >= 85) return 'bg-green-500';
    if (score >= 70) return 'bg-blue-500';
    if (score >= 50) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <div className="space-y-6">
      {/* Profile Selection */}
      {showProfileSelector && enhancedProfiles.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-blue-500" />
              Enhanced Content Generation
            </CardTitle>
            <CardDescription>
              Generate high-quality, personalized content using your comprehensive business profile
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <Label htmlFor="profile-select">Select Business Profile</Label>
                <Select
                  value={selectedProfile?.metadata.profileId || ''}
                  onValueChange={handleProfileSelect}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Choose a business profile" />
                  </SelectTrigger>
                  <SelectContent>
                    {enhancedProfiles.map((profile) => (
                      <SelectItem key={profile.metadata.profileId} value={profile.metadata.profileId}>
                        <div className="flex items-center justify-between w-full">
                          <span>{profile.coreInfo.businessName}</span>
                          <Badge 
                            variant="secondary" 
                            className={`ml-2 ${getProfileCompletionScore(profile) >= 80 ? 'bg-green-100' : 'bg-yellow-100'}`}
                          >
                            {getProfileCompletionScore(profile)}%
                          </Badge>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {selectedProfile && (
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>{selectedProfile.coreInfo.businessName}</strong> - {selectedProfile.coreInfo.industryCategory}
                    <br />
                    Profile Completion: {getProfileCompletionScore(selectedProfile)}% | 
                    Location: {selectedProfile.coreInfo.primaryLocation.city}, {selectedProfile.coreInfo.primaryLocation.country}
                    {selectedProfile.contentPreferences.localizationToggle && (
                      <span className="flex items-center gap-1 mt-1">
                        <Globe className="h-3 w-3" />
                        Local language integration enabled
                      </span>
                    )}
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Content Generation Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Content Settings</CardTitle>
          <CardDescription>Configure your content generation preferences</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="platform-select">Platform</Label>
              <Select value={platform} onValueChange={setPlatform}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {platforms.map((p) => (
                    <SelectItem key={p.value} value={p.value}>
                      {p.icon} {p.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="content-type-select">Content Type</Label>
              <Select value={contentType} onValueChange={setContentType}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {contentTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="custom-prompt">Custom Instructions (Optional)</Label>
            <Textarea
              id="custom-prompt"
              value={customPrompt}
              onChange={(e) => setCustomPrompt(e.target.value)}
              placeholder="Add specific instructions, themes, or requirements for your content..."
              rows={3}
            />
          </div>

          {selectedProfile?.contentPreferences.localizationToggle && (
            <div className="flex items-center space-x-2">
              <Switch
                id="use-localization"
                checked={useLocalization}
                onCheckedChange={setUseLocalization}
              />
              <Label htmlFor="use-localization" className="flex items-center gap-2">
                <Globe className="h-4 w-4" />
                Use local language integration
              </Label>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Generate Button */}
      <div className="flex justify-center">
        <Button 
          onClick={handleGenerateContent} 
          disabled={generating || !selectedProfile}
          size="lg"
          className="min-w-48"
        >
          {generating ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Generating...
            </>
          ) : (
            <>
              <Zap className="h-4 w-4 mr-2" />
              Generate Enhanced Content
            </>
          )}
        </Button>
      </div>

      {/* Generated Content Display */}
      {generatedContent && (
        <Card className="border-green-200 bg-green-50">
          <CardHeader>
            <div className="flex justify-between items-start">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  Generated Content
                </CardTitle>
                <CardDescription>
                  High-quality content with comprehensive business data integration
                </CardDescription>
              </div>
              <Badge className={getQualityBadgeColor(generatedContent.qualityScore)}>
                Quality: {generatedContent.qualityScore}%
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Content Sections */}
            <div className="space-y-4">
              {generatedContent.headline && (
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label className="font-medium">Headline</Label>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(generatedContent.headline, 'Headline')}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="p-3 bg-white rounded border">
                    <p className="font-semibold">{generatedContent.headline}</p>
                  </div>
                </div>
              )}

              {generatedContent.subheadline && (
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label className="font-medium">Subheadline</Label>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(generatedContent.subheadline!, 'Subheadline')}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="p-3 bg-white rounded border">
                    <p>{generatedContent.subheadline}</p>
                  </div>
                </div>
              )}

              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label className="font-medium">Caption</Label>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(generatedContent.caption, 'Caption')}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
                <div className="p-3 bg-white rounded border">
                  <p className="whitespace-pre-wrap">{generatedContent.caption}</p>
                </div>
              </div>

              {generatedContent.callToAction && (
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label className="font-medium">Call to Action</Label>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(generatedContent.callToAction!, 'Call to Action')}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="p-3 bg-white rounded border">
                    <p className="font-medium text-blue-600">{generatedContent.callToAction}</p>
                  </div>
                </div>
              )}

              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label className="font-medium">Hashtags</Label>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(generatedContent.hashtags.join(' '), 'Hashtags')}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
                <div className="p-3 bg-white rounded border">
                  <p className="text-blue-600">{generatedContent.hashtags.join(' ')}</p>
                </div>
              </div>
            </div>

            {/* Quality Insights */}
            <div className="space-y-3 pt-4 border-t">
              <h4 className="font-medium">Content Quality Insights</h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Quality Score:</span>
                  <Progress value={generatedContent.qualityScore} className="mt-1" />
                </div>
                <div>
                  <span className="font-medium">Features Used:</span>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {generatedContent.localizationApplied && (
                      <Badge variant="secondary" className="text-xs">
                        <Globe className="h-3 w-3 mr-1" />
                        Localized
                      </Badge>
                    )}
                    {generatedContent.dataSourcesUsed.map((source) => (
                      <Badge key={source} variant="outline" className="text-xs">
                        {source}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>

              {generatedContent.improvementSuggestions.length > 0 && (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Improvement Suggestions:</strong>
                    <ul className="mt-1 space-y-1">
                      {generatedContent.improvementSuggestions.map((suggestion, index) => (
                        <li key={index} className="text-sm">• {suggestion}</li>
                      ))}
                    </ul>
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* No Enhanced Profiles Message */}
      {enhancedProfiles.length === 0 && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>Enhanced profiles required:</strong> Create or migrate to an enhanced business profile to use advanced content generation features.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}
