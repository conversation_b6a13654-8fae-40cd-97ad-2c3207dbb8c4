'use client';

/**
 * Profile Analytics Dashboard
 * Shows completion scores, content quality metrics, and improvement suggestions
 */

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  BarChart3, 
  TrendingUp, 
  Target, 
  CheckCircle, 
  AlertTriangle, 
  Info,
  Globe,
  Zap,
  Users,
  Star
} from 'lucide-react';
import { useEnhancedBrand } from '@/contexts/enhanced-brand-context';
import type { EnhancedBusinessProfile } from '@/lib/types/enhanced-business-profile';

interface ProfileAnalytics {
  completionScore: number;
  sectionsCompleted: number;
  totalSections: number;
  strengths: string[];
  improvements: string[];
  recommendations: string[];
  qualityMetrics: {
    dataRichness: number;
    trustIndicators: number;
    localizationReady: boolean;
    contentOptimization: number;
  };
}

export function ProfileAnalyticsDashboard() {
  const { enhancedProfiles, currentEnhancedProfile, selectEnhancedProfile } = useEnhancedBrand();
  const [selectedProfileId, setSelectedProfileId] = useState<string>(
    currentEnhancedProfile?.metadata.profileId || ''
  );

  const selectedProfile = useMemo(() => {
    return enhancedProfiles.find(p => p.metadata.profileId === selectedProfileId) || enhancedProfiles[0];
  }, [enhancedProfiles, selectedProfileId]);

  const analytics = useMemo((): ProfileAnalytics | null => {
    if (!selectedProfile) return null;

    return analyzeProfile(selectedProfile);
  }, [selectedProfile]);

  const handleProfileSelect = (profileId: string) => {
    setSelectedProfileId(profileId);
    const profile = enhancedProfiles.find(p => p.metadata.profileId === profileId);
    if (profile) {
      selectEnhancedProfile(profile);
    }
  };

  if (enhancedProfiles.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Profile Analytics</CardTitle>
          <CardDescription>Analyze your business profile performance and optimization opportunities</CardDescription>
        </CardHeader>
        <CardContent>
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              No enhanced business profiles found. Create an enhanced profile to view analytics and optimization insights.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  if (!selectedProfile || !analytics) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p>Loading analytics...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Profile Selector */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-blue-500" />
                Profile Analytics Dashboard
              </CardTitle>
              <CardDescription>
                Comprehensive analysis of your business profile performance
              </CardDescription>
            </div>
            {enhancedProfiles.length > 1 && (
              <select
                value={selectedProfileId}
                onChange={(e) => handleProfileSelect(e.target.value)}
                className="px-3 py-2 border rounded-md bg-background"
              >
                {enhancedProfiles.map((profile) => (
                  <option key={profile.metadata.profileId} value={profile.metadata.profileId}>
                    {profile.coreInfo.businessName}
                  </option>
                ))}
              </select>
            )}
          </div>
        </CardHeader>
      </Card>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Completion Score</p>
                <p className="text-2xl font-bold">{analytics.completionScore}%</p>
              </div>
              <Target className={`h-8 w-8 ${analytics.completionScore >= 80 ? 'text-green-500' : analytics.completionScore >= 60 ? 'text-yellow-500' : 'text-red-500'}`} />
            </div>
            <Progress value={analytics.completionScore} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Sections Complete</p>
                <p className="text-2xl font-bold">{analytics.sectionsCompleted}/{analytics.totalSections}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-blue-500" />
            </div>
            <Progress value={(analytics.sectionsCompleted / analytics.totalSections) * 100} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Content Quality</p>
                <p className="text-2xl font-bold">{analytics.qualityMetrics.contentOptimization}%</p>
              </div>
              <Star className="h-8 w-8 text-yellow-500" />
            </div>
            <Progress value={analytics.qualityMetrics.contentOptimization} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Trust Score</p>
                <p className="text-2xl font-bold">{analytics.qualityMetrics.trustIndicators}%</p>
              </div>
              <Users className="h-8 w-8 text-purple-500" />
            </div>
            <Progress value={analytics.qualityMetrics.trustIndicators} className="mt-2" />
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="strengths">Strengths</TabsTrigger>
          <TabsTrigger value="improvements">Improvements</TabsTrigger>
          <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Profile Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Profile Summary</CardTitle>
                <CardDescription>{selectedProfile.coreInfo.businessName}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Industry:</span>
                    <p>{selectedProfile.coreInfo.industryCategory}</p>
                  </div>
                  <div>
                    <span className="font-medium">Business Model:</span>
                    <p>{selectedProfile.coreInfo.businessModel}</p>
                  </div>
                  <div>
                    <span className="font-medium">Location:</span>
                    <p>{selectedProfile.coreInfo.primaryLocation.city}, {selectedProfile.coreInfo.primaryLocation.country}</p>
                  </div>
                  <div>
                    <span className="font-medium">Established:</span>
                    <p>{selectedProfile.coreInfo.establishmentYear}</p>
                  </div>
                </div>

                <div className="flex flex-wrap gap-2">
                  {selectedProfile.contentPreferences.localizationToggle && (
                    <Badge variant="secondary" className="flex items-center gap-1">
                      <Globe className="h-3 w-3" />
                      Localization Enabled
                    </Badge>
                  )}
                  {selectedProfile.offerings && selectedProfile.offerings.length > 0 && (
                    <Badge variant="secondary">
                      {selectedProfile.offerings.length} Offerings
                    </Badge>
                  )}
                  {analytics.qualityMetrics.dataRichness >= 80 && (
                    <Badge variant="secondary" className="flex items-center gap-1">
                      <Zap className="h-3 w-3" />
                      Data Rich
                    </Badge>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Quality Metrics */}
            <Card>
              <CardHeader>
                <CardTitle>Quality Metrics</CardTitle>
                <CardDescription>Key performance indicators for content generation</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Data Richness</span>
                      <span>{analytics.qualityMetrics.dataRichness}%</span>
                    </div>
                    <Progress value={analytics.qualityMetrics.dataRichness} />
                  </div>

                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Trust Indicators</span>
                      <span>{analytics.qualityMetrics.trustIndicators}%</span>
                    </div>
                    <Progress value={analytics.qualityMetrics.trustIndicators} />
                  </div>

                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Content Optimization</span>
                      <span>{analytics.qualityMetrics.contentOptimization}%</span>
                    </div>
                    <Progress value={analytics.qualityMetrics.contentOptimization} />
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm">Localization Ready</span>
                    <Badge variant={analytics.qualityMetrics.localizationReady ? "default" : "secondary"}>
                      {analytics.qualityMetrics.localizationReady ? "Yes" : "No"}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="strengths">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                Profile Strengths
              </CardTitle>
              <CardDescription>Areas where your profile excels</CardDescription>
            </CardHeader>
            <CardContent>
              {analytics.strengths.length > 0 ? (
                <div className="space-y-3">
                  {analytics.strengths.map((strength, index) => (
                    <div key={index} className="flex items-start gap-3 p-3 bg-green-50 rounded-lg">
                      <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                      <p className="text-sm">{strength}</p>
                    </div>
                  ))}
                </div>
              ) : (
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    Complete more profile sections to identify your strengths.
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="improvements">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-yellow-500" />
                Areas for Improvement
              </CardTitle>
              <CardDescription>Opportunities to enhance your profile</CardDescription>
            </CardHeader>
            <CardContent>
              {analytics.improvements.length > 0 ? (
                <div className="space-y-3">
                  {analytics.improvements.map((improvement, index) => (
                    <div key={index} className="flex items-start gap-3 p-3 bg-yellow-50 rounded-lg">
                      <AlertTriangle className="h-5 w-5 text-yellow-500 mt-0.5 flex-shrink-0" />
                      <p className="text-sm">{improvement}</p>
                    </div>
                  ))}
                </div>
              ) : (
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>
                    Great job! No major improvements needed at this time.
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="recommendations">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-blue-500" />
                Optimization Recommendations
              </CardTitle>
              <CardDescription>Actionable steps to improve your profile performance</CardDescription>
            </CardHeader>
            <CardContent>
              {analytics.recommendations.length > 0 ? (
                <div className="space-y-3">
                  {analytics.recommendations.map((recommendation, index) => (
                    <div key={index} className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg">
                      <TrendingUp className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                      <div>
                        <p className="text-sm">{recommendation}</p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    Your profile is well-optimized! Check back after making updates for new recommendations.
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Helper function to analyze profile
function analyzeProfile(profile: EnhancedBusinessProfile): ProfileAnalytics {
  const strengths: string[] = [];
  const improvements: string[] = [];
  const recommendations: string[] = [];

  // Calculate completion score
  let completionScore = 0;
  let sectionsCompleted = 0;
  const totalSections = 6;

  // Core info analysis (20 points)
  if (profile.coreInfo.businessName && profile.coreInfo.industryCategory && 
      profile.coreInfo.primaryLocation.city && profile.coreInfo.primaryLocation.country) {
    completionScore += 20;
    sectionsCompleted += 1;
    strengths.push("Complete core business information provides strong foundation for content generation");
  } else {
    improvements.push("Complete all core business information fields");
  }

  // Offerings analysis (20 points)
  if (profile.offerings && profile.offerings.length > 0) {
    completionScore += 20;
    sectionsCompleted += 1;
    strengths.push(`${profile.offerings.length} products/services documented for targeted content creation`);
    
    if (profile.offerings.some(o => o.pricingStructure?.basePrice || o.pricingStructure?.hourlyRate)) {
      strengths.push("Pricing information available for promotional content");
    } else {
      recommendations.push("Add pricing information to offerings for more effective promotional content");
    }
  } else {
    improvements.push("Add your products and services to enable targeted content generation");
  }

  // Trust indicators analysis (15 points)
  if (profile.trustIndicators) {
    let trustScore = 0;
    if (profile.trustIndicators.businessLongevity && profile.trustIndicators.businessLongevity > 0) {
      trustScore += 5;
      strengths.push(`${profile.trustIndicators.businessLongevity} years of business experience builds credibility`);
    }
    if (profile.trustIndicators.customerBase?.customersServed) {
      trustScore += 5;
      strengths.push(`${profile.trustIndicators.customerBase.customersServed} customers served demonstrates market validation`);
    }
    if (profile.trustIndicators.reviewScores?.google || profile.trustIndicators.reviewScores?.yelp) {
      trustScore += 5;
      strengths.push("Online review scores available for social proof");
    }
    
    if (trustScore >= 10) {
      completionScore += 15;
      sectionsCompleted += 1;
    } else {
      improvements.push("Add more trust indicators (customer count, reviews, certifications)");
    }
  } else {
    improvements.push("Add trust indicators to build credibility in your content");
  }

  // Operational params analysis (15 points)
  if (profile.operationalParams?.contactChannels) {
    let contactScore = 0;
    if (profile.operationalParams.contactChannels.phone) contactScore += 5;
    if (profile.operationalParams.contactChannels.email) contactScore += 5;
    if (profile.operationalParams.contactChannels.website) contactScore += 5;
    
    if (contactScore >= 10) {
      completionScore += 15;
      sectionsCompleted += 1;
      strengths.push("Multiple contact channels available for customer engagement");
    } else {
      improvements.push("Add more contact channels (phone, email, website)");
    }
  } else {
    improvements.push("Add contact information for customer accessibility");
  }

  // Content preferences analysis (15 points)
  if (profile.contentPreferences?.brandVoice || profile.contentPreferences?.contentThemes?.length) {
    completionScore += 15;
    sectionsCompleted += 1;
    strengths.push("Brand voice and content preferences defined for consistent messaging");
    
    if (profile.contentPreferences.localizationToggle) {
      strengths.push("Localization enabled for culturally relevant content");
    }
  } else {
    improvements.push("Define your brand voice and content preferences");
  }

  // Competitive differentiation analysis (15 points)
  if (profile.competitiveDifferentiation && Object.keys(profile.competitiveDifferentiation).length > 0) {
    completionScore += 15;
    sectionsCompleted += 1;
    strengths.push("Competitive advantages documented for unique positioning");
  } else {
    improvements.push("Add competitive differentiation factors to stand out in your market");
  }

  // Quality metrics calculation
  const dataRichness = Math.min(100, (profile.offerings?.length || 0) * 20 + 
    (profile.trustIndicators?.businessLongevity ? 20 : 0) + 
    (Object.keys(profile.competitiveDifferentiation || {}).length * 15));

  const trustIndicators = Math.min(100, 
    (profile.trustIndicators?.businessLongevity ? 25 : 0) +
    (profile.trustIndicators?.customerBase?.customersServed ? 25 : 0) +
    (profile.trustIndicators?.reviewScores?.google ? 25 : 0) +
    (profile.trustIndicators?.professionalCredentials?.certifications?.length ? 25 : 0));

  const contentOptimization = Math.min(100,
    (profile.contentPreferences?.brandVoice ? 30 : 0) +
    (profile.contentPreferences?.contentThemes?.length ? 20 : 0) +
    (profile.contentPreferences?.visualStyle ? 20 : 0) +
    (profile.contentPreferences?.localizationToggle ? 30 : 0));

  // Generate recommendations
  if (completionScore < 80) {
    recommendations.push("Aim for 80%+ completion score for optimal content generation quality");
  }
  
  if (!profile.contentPreferences?.localizationToggle && profile.coreInfo.primaryLocation.country !== 'United States') {
    recommendations.push("Enable localization to include local language phrases and cultural references");
  }
  
  if (!profile.offerings || profile.offerings.length < 3) {
    recommendations.push("Add more products/services (aim for 3+) to create diverse content topics");
  }
  
  if (trustIndicators < 50) {
    recommendations.push("Build trust indicators by adding customer testimonials, certifications, or awards");
  }

  return {
    completionScore: Math.min(100, completionScore),
    sectionsCompleted,
    totalSections,
    strengths,
    improvements,
    recommendations,
    qualityMetrics: {
      dataRichness,
      trustIndicators,
      localizationReady: profile.contentPreferences?.localizationToggle || false,
      contentOptimization
    }
  };
}
