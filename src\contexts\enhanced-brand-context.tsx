'use client';

/**
 * Enhanced Brand Context
 * Integrates both legacy brand profiles and enhanced business profiles
 */

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/hooks/use-auth-supabase';
import { enhancedBusinessProfileService } from '@/lib/supabase/services/enhanced-business-profile-service';
import type { EnhancedBusinessProfile } from '@/lib/types/enhanced-business-profile';
import type { CompleteBrandProfile } from '@/components/cbrand/cbrand-wizard';

interface EnhancedBrandContextType {
  // Enhanced profiles
  enhancedProfiles: EnhancedBusinessProfile[];
  currentEnhancedProfile: EnhancedBusinessProfile | null;
  
  // Legacy profiles (for backward compatibility)
  legacyProfiles: CompleteBrandProfile[];
  currentLegacyProfile: CompleteBrandProfile | null;
  
  // Combined state
  allProfiles: (EnhancedBusinessProfile | CompleteBrandProfile)[];
  currentProfile: EnhancedBusinessProfile | CompleteBrandProfile | null;
  profileType: 'enhanced' | 'legacy' | null;
  
  // Loading states
  loading: boolean;
  saving: boolean;
  migrating: boolean;
  
  // Actions for enhanced profiles
  selectEnhancedProfile: (profile: EnhancedBusinessProfile | null) => void;
  saveEnhancedProfile: (profile: EnhancedBusinessProfile) => Promise<string>;
  updateEnhancedProfile: (profileId: string, updates: Partial<EnhancedBusinessProfile>) => Promise<void>;
  deleteEnhancedProfile: (profileId: string) => Promise<void>;
  
  // Actions for legacy profiles
  selectLegacyProfile: (profile: CompleteBrandProfile | null) => void;
  
  // Migration actions
  migrateToEnhanced: (legacyProfile: CompleteBrandProfile) => Promise<EnhancedBusinessProfile>;
  
  // Utility actions
  refreshProfiles: () => Promise<void>;
  getProfileCompletionScore: (profile: EnhancedBusinessProfile | CompleteBrandProfile) => number;
  
  // Error handling
  error: string | null;
}

const EnhancedBrandContext = createContext<EnhancedBrandContextType | undefined>(undefined);

interface EnhancedBrandProviderProps {
  children: React.ReactNode;
}

export function EnhancedBrandProvider({ children }: EnhancedBrandProviderProps) {
  const { user, getAccessToken } = useAuth();
  
  // State
  const [enhancedProfiles, setEnhancedProfiles] = useState<EnhancedBusinessProfile[]>([]);
  const [currentEnhancedProfile, setCurrentEnhancedProfile] = useState<EnhancedBusinessProfile | null>(null);
  const [legacyProfiles, setLegacyProfiles] = useState<CompleteBrandProfile[]>([]);
  const [currentLegacyProfile, setCurrentLegacyProfile] = useState<CompleteBrandProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [migrating, setMigrating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Computed values
  const allProfiles = [...enhancedProfiles, ...legacyProfiles];
  const currentProfile = currentEnhancedProfile || currentLegacyProfile;
  const profileType = currentEnhancedProfile ? 'enhanced' : currentLegacyProfile ? 'legacy' : null;

  // Load enhanced profiles
  const loadEnhancedProfiles = useCallback(async () => {
    if (!user?.userId) return;

    try {
      console.log('📖 [Enhanced Brand Context] Loading enhanced profiles...');
      const profiles = await enhancedBusinessProfileService.loadUserEnhancedProfiles(user.userId);
      setEnhancedProfiles(profiles);
      
      // Auto-select first profile if none selected
      if (profiles.length > 0 && !currentEnhancedProfile) {
        setCurrentEnhancedProfile(profiles[0]);
      }
      
      console.log(`✅ [Enhanced Brand Context] Loaded ${profiles.length} enhanced profiles`);
    } catch (error) {
      console.error('❌ [Enhanced Brand Context] Error loading enhanced profiles:', error);
      setError(error instanceof Error ? error.message : 'Failed to load enhanced profiles');
    }
  }, [user?.userId, currentEnhancedProfile]);

  // Load legacy profiles
  const loadLegacyProfiles = useCallback(async () => {
    if (!user?.userId) return;

    try {
      console.log('📖 [Enhanced Brand Context] Loading legacy profiles...');
      const token = await getAccessToken();
      if (!token) return;

      const response = await fetch('/api/brand-profiles', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const profiles = await response.json();
        setLegacyProfiles(profiles);
        console.log(`✅ [Enhanced Brand Context] Loaded ${profiles.length} legacy profiles`);
      }
    } catch (error) {
      console.error('❌ [Enhanced Brand Context] Error loading legacy profiles:', error);
    }
  }, [user?.userId, getAccessToken]);

  // Load all profiles
  const refreshProfiles = useCallback(async () => {
    if (!user?.userId) return;

    setLoading(true);
    setError(null);

    try {
      await Promise.all([
        loadEnhancedProfiles(),
        loadLegacyProfiles()
      ]);
    } catch (error) {
      console.error('❌ [Enhanced Brand Context] Error refreshing profiles:', error);
      setError('Failed to refresh profiles');
    } finally {
      setLoading(false);
    }
  }, [user?.userId, loadEnhancedProfiles, loadLegacyProfiles]);

  // Enhanced profile actions
  const selectEnhancedProfile = useCallback((profile: EnhancedBusinessProfile | null) => {
    setCurrentEnhancedProfile(profile);
    setCurrentLegacyProfile(null); // Clear legacy selection
    
    // Persist selection
    if (profile) {
      localStorage.setItem('selectedEnhancedProfileId', profile.metadata.profileId);
      localStorage.removeItem('selectedLegacyProfileId');
    } else {
      localStorage.removeItem('selectedEnhancedProfileId');
    }
  }, []);

  const saveEnhancedProfile = useCallback(async (profile: EnhancedBusinessProfile): Promise<string> => {
    if (!user?.userId) throw new Error('User not authenticated');

    setSaving(true);
    setError(null);

    try {
      console.log('💾 [Enhanced Brand Context] Saving enhanced profile:', profile.coreInfo.businessName);
      
      // Ensure user ID is set
      profile.metadata.userId = user.userId;
      
      const profileId = await enhancedBusinessProfileService.saveEnhancedProfile(profile);
      
      // Refresh profiles to get updated data
      await loadEnhancedProfiles();
      
      console.log('✅ [Enhanced Brand Context] Enhanced profile saved successfully:', profileId);
      return profileId;
      
    } catch (error) {
      console.error('❌ [Enhanced Brand Context] Error saving enhanced profile:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to save enhanced profile';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setSaving(false);
    }
  }, [user?.userId, loadEnhancedProfiles]);

  const updateEnhancedProfile = useCallback(async (profileId: string, updates: Partial<EnhancedBusinessProfile>) => {
    if (!user?.userId) throw new Error('User not authenticated');

    setSaving(true);
    setError(null);

    try {
      console.log('🔄 [Enhanced Brand Context] Updating enhanced profile:', profileId);
      
      // Find the existing profile
      const existingProfile = enhancedProfiles.find(p => p.metadata.profileId === profileId);
      if (!existingProfile) {
        throw new Error('Profile not found');
      }

      // Merge updates
      const updatedProfile: EnhancedBusinessProfile = {
        ...existingProfile,
        ...updates,
        metadata: {
          ...existingProfile.metadata,
          ...updates.metadata,
          userId: user.userId,
          updatedAt: new Date()
        }
      };

      await enhancedBusinessProfileService.saveEnhancedProfile(updatedProfile);
      
      // Refresh profiles
      await loadEnhancedProfiles();
      
      console.log('✅ [Enhanced Brand Context] Enhanced profile updated successfully');
      
    } catch (error) {
      console.error('❌ [Enhanced Brand Context] Error updating enhanced profile:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to update enhanced profile';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setSaving(false);
    }
  }, [user?.userId, enhancedProfiles, loadEnhancedProfiles]);

  const deleteEnhancedProfile = useCallback(async (profileId: string) => {
    if (!user?.userId) throw new Error('User not authenticated');

    setSaving(true);
    setError(null);

    try {
      console.log('🗑️ [Enhanced Brand Context] Deleting enhanced profile:', profileId);
      
      await enhancedBusinessProfileService.deleteEnhancedProfile(profileId, user.userId);
      
      // Clear selection if deleted profile was selected
      if (currentEnhancedProfile?.metadata.profileId === profileId) {
        setCurrentEnhancedProfile(null);
        localStorage.removeItem('selectedEnhancedProfileId');
      }
      
      // Refresh profiles
      await loadEnhancedProfiles();
      
      console.log('✅ [Enhanced Brand Context] Enhanced profile deleted successfully');
      
    } catch (error) {
      console.error('❌ [Enhanced Brand Context] Error deleting enhanced profile:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete enhanced profile';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setSaving(false);
    }
  }, [user?.userId, currentEnhancedProfile, loadEnhancedProfiles]);

  // Legacy profile actions
  const selectLegacyProfile = useCallback((profile: CompleteBrandProfile | null) => {
    setCurrentLegacyProfile(profile);
    setCurrentEnhancedProfile(null); // Clear enhanced selection
    
    // Persist selection
    if (profile) {
      localStorage.setItem('selectedLegacyProfileId', profile.id || '');
      localStorage.removeItem('selectedEnhancedProfileId');
    } else {
      localStorage.removeItem('selectedLegacyProfileId');
    }
  }, []);

  // Migration action
  const migrateToEnhanced = useCallback(async (legacyProfile: CompleteBrandProfile): Promise<EnhancedBusinessProfile> => {
    if (!user?.userId) throw new Error('User not authenticated');

    setMigrating(true);
    setError(null);

    try {
      console.log('🔄 [Enhanced Brand Context] Migrating legacy profile to enhanced:', legacyProfile.businessName);
      
      // Convert legacy profile to enhanced profile
      const enhancedProfile = await enhancedBusinessProfileService.convertLegacyProfile(legacyProfile, user.userId);
      
      // Save the enhanced profile
      const profileId = await enhancedBusinessProfileService.saveEnhancedProfile(enhancedProfile);
      enhancedProfile.metadata.profileId = profileId;
      
      // Refresh profiles
      await loadEnhancedProfiles();
      
      // Auto-select the new enhanced profile
      setCurrentEnhancedProfile(enhancedProfile);
      setCurrentLegacyProfile(null);
      
      console.log('✅ [Enhanced Brand Context] Legacy profile migrated successfully:', profileId);
      return enhancedProfile;
      
    } catch (error) {
      console.error('❌ [Enhanced Brand Context] Error migrating profile:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to migrate profile';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setMigrating(false);
    }
  }, [user?.userId, loadEnhancedProfiles]);

  // Utility function to get completion score
  const getProfileCompletionScore = useCallback((profile: EnhancedBusinessProfile | CompleteBrandProfile): number => {
    if ('metadata' in profile && profile.metadata?.completionScore !== undefined) {
      return profile.metadata.completionScore;
    }
    
    // Calculate basic completion score for legacy profiles
    let score = 0;
    if ('businessName' in profile && profile.businessName) score += 20;
    if ('businessType' in profile && profile.businessType) score += 20;
    if ('location' in profile && profile.location) score += 20;
    if ('description' in profile && profile.description) score += 20;
    if ('services' in profile && profile.services && profile.services.length > 0) score += 20;
    
    return score;
  }, []);

  // Load profiles on mount and user change
  useEffect(() => {
    if (user?.userId) {
      refreshProfiles();
    } else {
      setEnhancedProfiles([]);
      setLegacyProfiles([]);
      setCurrentEnhancedProfile(null);
      setCurrentLegacyProfile(null);
      setLoading(false);
    }
  }, [user?.userId, refreshProfiles]);

  // Restore selected profile from localStorage
  useEffect(() => {
    if (enhancedProfiles.length > 0 || legacyProfiles.length > 0) {
      const savedEnhancedId = localStorage.getItem('selectedEnhancedProfileId');
      const savedLegacyId = localStorage.getItem('selectedLegacyProfileId');
      
      if (savedEnhancedId) {
        const profile = enhancedProfiles.find(p => p.metadata.profileId === savedEnhancedId);
        if (profile) {
          setCurrentEnhancedProfile(profile);
          return;
        }
      }
      
      if (savedLegacyId) {
        const profile = legacyProfiles.find(p => p.id === savedLegacyId);
        if (profile) {
          setCurrentLegacyProfile(profile);
          return;
        }
      }
      
      // Auto-select first available profile
      if (enhancedProfiles.length > 0) {
        setCurrentEnhancedProfile(enhancedProfiles[0]);
      } else if (legacyProfiles.length > 0) {
        setCurrentLegacyProfile(legacyProfiles[0]);
      }
    }
  }, [enhancedProfiles, legacyProfiles]);

  const contextValue: EnhancedBrandContextType = {
    enhancedProfiles,
    currentEnhancedProfile,
    legacyProfiles,
    currentLegacyProfile,
    allProfiles,
    currentProfile,
    profileType,
    loading,
    saving,
    migrating,
    selectEnhancedProfile,
    saveEnhancedProfile,
    updateEnhancedProfile,
    deleteEnhancedProfile,
    selectLegacyProfile,
    migrateToEnhanced,
    refreshProfiles,
    getProfileCompletionScore,
    error
  };

  return (
    <EnhancedBrandContext.Provider value={contextValue}>
      {children}
    </EnhancedBrandContext.Provider>
  );
}

// Hook to use the enhanced brand context
export function useEnhancedBrand() {
  const context = useContext(EnhancedBrandContext);
  if (context === undefined) {
    throw new Error('useEnhancedBrand must be used within an EnhancedBrandProvider');
  }
  return context;
}

// Convenience hooks
export function useCurrentProfile() {
  const { currentProfile, profileType } = useEnhancedBrand();
  return { currentProfile, profileType };
}

export function useEnhancedProfiles() {
  const { enhancedProfiles, currentEnhancedProfile, selectEnhancedProfile } = useEnhancedBrand();
  return { enhancedProfiles, currentEnhancedProfile, selectEnhancedProfile };
}

export function useLegacyProfiles() {
  const { legacyProfiles, currentLegacyProfile, selectLegacyProfile } = useEnhancedBrand();
  return { legacyProfiles, currentLegacyProfile, selectLegacyProfile };
}

export function useProfileMigration() {
  const { migrateToEnhanced, migrating } = useEnhancedBrand();
  return { migrateToEnhanced, migrating };
}
