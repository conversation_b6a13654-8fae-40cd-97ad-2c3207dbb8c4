import { BrandProfile } from '@/lib/types';

export interface BusinessIntelligenceAnalysis {
  specificBenefits: string[];
  uniqueValuePropositions: string[];
  competitiveAdvantages: string[];
  targetPainPoints: string[];
  serviceHighlights: string[];
  customerSuccessStories: string[];
  businessStrengths: string[];
  marketPositioning: string;
  contentOpportunities: string[];
  personalizedMessaging: {
    headlines: string[];
    subheadlines: string[];
    captions: string[];
    ctas: string[];
  };
}

export class BusinessIntelligenceAnalyzer {
  private static instance: BusinessIntelligenceAnalyzer;
  private analysisCache = new Map<string, BusinessIntelligenceAnalysis>();

  static getInstance(): BusinessIntelligenceAnalyzer {
    if (!BusinessIntelligenceAnalyzer.instance) {
      BusinessIntelligenceAnalyzer.instance = new BusinessIntelligenceAnalyzer();
    }
    return BusinessIntelligenceAnalyzer.instance;
  }

  async analyzeBusiness(brandProfile: BrandProfile): Promise<BusinessIntelligenceAnalysis> {
    const cacheKey = `${brandProfile.businessName}-${brandProfile.businessType}-${brandProfile.location}`;
    
    if (this.analysisCache.has(cacheKey)) {
      return this.analysisCache.get(cacheKey)!;
    }

    const analysis = await this.performDeepAnalysis(brandProfile);
    this.analysisCache.set(cacheKey, analysis);
    return analysis;
  }

  private async performDeepAnalysis(brandProfile: BrandProfile): Promise<BusinessIntelligenceAnalysis> {
    const {
      businessName,
      businessType,
      location,
      description = '',
      services = '',
      keyFeatures = '',
      competitiveAdvantages = '',
      targetAudience = '',
      websiteUrl = ''
    } = brandProfile;

    // Extract services and features
    const servicesList = this.parseTextToList(services);
    const featuresList = this.parseTextToList(keyFeatures);
    const advantagesList = this.parseTextToList(competitiveAdvantages);

    // Analyze business description for specific benefits
    const specificBenefits = await this.extractSpecificBenefits(description, servicesList, featuresList, businessType);
    
    // Generate unique value propositions based on actual business data
    const uniqueValuePropositions = await this.generateValuePropositions(brandProfile, specificBenefits);
    
    // Identify competitive advantages from business data
    const identifiedCompetitiveAdvantages = await this.identifyCompetitiveAdvantages(brandProfile, advantagesList);
    
    // Extract target pain points based on business type and services
    const targetPainPoints = await this.identifyTargetPainPoints(businessType, servicesList, targetAudience);
    
    // Highlight specific services
    const serviceHighlights = await this.highlightServices(servicesList, businessType, location);
    
    // Generate customer success stories based on business strengths
    const customerSuccessStories = await this.generateSuccessStories(brandProfile, specificBenefits);
    
    // Identify business strengths from available data
    const businessStrengths = await this.identifyBusinessStrengths(brandProfile, specificBenefits);
    
    // Determine market positioning
    const marketPositioning = await this.determineMarketPositioning(brandProfile, identifiedCompetitiveAdvantages);
    
    // Identify content opportunities
    const contentOpportunities = await this.identifyContentOpportunities(brandProfile, specificBenefits);
    
    // Generate personalized messaging
    const personalizedMessaging = await this.generatePersonalizedMessaging(brandProfile, specificBenefits);

    return {
      specificBenefits,
      uniqueValuePropositions,
      competitiveAdvantages: identifiedCompetitiveAdvantages,
      targetPainPoints,
      serviceHighlights,
      customerSuccessStories,
      businessStrengths,
      marketPositioning,
      contentOpportunities,
      personalizedMessaging
    };
  }

  private parseTextToList(text: string): string[] {
    if (!text) return [];
    return text.split('\n')
      .map(item => item.trim())
      .filter(item => item.length > 0);
  }

  private async extractSpecificBenefits(description: string, services: string[], features: string[], businessType: string): Promise<string[]> {
    const benefits: string[] = [];
    
    // Extract benefits from description
    const descriptionBenefits = this.extractBenefitsFromText(description);
    benefits.push(...descriptionBenefits);
    
    // Extract benefits from services - use actual service data
    const serviceBenefits = services.map(service => this.convertServiceToBenefit(service, businessType));
    benefits.push(...serviceBenefits);
    
    // Extract benefits from features
    const featureBenefits = features.map(feature => this.convertFeatureToBenefit(feature, businessType));
    benefits.push(...featureBenefits);
    
    // Remove duplicates and filter out generic benefits
    return [...new Set(benefits)].filter(benefit => 
      !this.isGenericBenefit(benefit) && benefit.length > 10
    );
  }

  private extractBenefitsFromText(text: string): string[] {
    const benefits: string[] = [];
    
    // Look for benefit indicators
    const benefitPatterns = [
      /(?:saves|reduces|increases|improves|provides|offers|delivers|ensures|guarantees|enables|helps|allows|makes|gives|brings)\s+[^.!?]+/gi,
      /(?:faster|quicker|easier|better|more|less|safer|reliable|convenient|affordable|premium|professional|expert|local|personalized|customized)\s+[^.!?]+/gi
    ];
    
    benefitPatterns.forEach(pattern => {
      const matches = text.match(pattern);
      if (matches) {
        benefits.push(...matches.map(match => match.trim()));
      }
    });
    
    return benefits;
  }

  private convertServiceToBenefit(service: string, businessType: string): string {
    // Convert service descriptions to benefit statements
    const serviceLower = service.toLowerCase();
    
    if (serviceLower.includes('consultation') || serviceLower.includes('advice')) {
      return `Expert ${businessType} consultation and personalized advice`;
    }
    
    if (serviceLower.includes('delivery') || serviceLower.includes('shipping')) {
      return `Fast and reliable delivery service`;
    }
    
    if (serviceLower.includes('support') || serviceLower.includes('help')) {
      return `24/7 customer support and assistance`;
    }
    
    if (serviceLower.includes('custom') || serviceLower.includes('personalized')) {
      return `Customized solutions tailored to your needs`;
    }
    
    if (serviceLower.includes('free') || serviceLower.includes('no cost')) {
      return `Free service with no hidden costs`;
    }
    
    if (serviceLower.includes('guarantee') || serviceLower.includes('warranty')) {
      return `Money-back guarantee and warranty protection`;
    }
    
    // Default benefit based on service - make it more specific and valuable
    if (service.toLowerCase().includes('banking') || service.toLowerCase().includes('finance')) {
      return `Secure and reliable financial services with competitive rates`;
    }
    
    if (service.toLowerCase().includes('payment') || service.toLowerCase().includes('transfer')) {
      return `Fast, secure payment processing with instant transfers`;
    }
    
    if (service.toLowerCase().includes('loan') || service.toLowerCase().includes('credit')) {
      return `Quick loan approval with flexible repayment terms`;
    }
    
    if (service.toLowerCase().includes('cookies') || service.toLowerCase().includes('food')) {
      return `Fresh, delicious baked goods made with premium ingredients`;
    }
    
    if (service.toLowerCase().includes('electronics') || service.toLowerCase().includes('tech')) {
      return `Latest technology products with expert technical support`;
    }
    
    // Generic fallback - make it more specific
    return `Expert ${service.toLowerCase()} solutions tailored to your needs`;
  }

  private convertFeatureToBenefit(feature: string, businessType: string): string {
    const featureLower = feature.toLowerCase();
    
    if (featureLower.includes('24/7') || featureLower.includes('24 hours')) {
      return `Round-the-clock availability and support`;
    }
    
    if (featureLower.includes('mobile') || featureLower.includes('app')) {
      return `Mobile-friendly and convenient access`;
    }
    
    if (featureLower.includes('secure') || featureLower.includes('safe')) {
      return `Bank-level security and data protection`;
    }
    
    if (featureLower.includes('instant') || featureLower.includes('immediate')) {
      return `Instant processing and immediate results`;
    }
    
    if (featureLower.includes('local') || featureLower.includes('nearby')) {
      return `Local expertise and community focus`;
    }
    
    if (featureLower.includes('award') || featureLower.includes('certified')) {
      return `Award-winning and certified quality`;
    }
    
    // Default benefit based on feature
    return `Advanced ${feature} capabilities`;
  }

  private isGenericBenefit(benefit: string): boolean {
    const genericPhrases = [
      'quality service',
      'excellent service',
      'good service',
      'best service',
      'professional service',
      'reliable service',
      'trusted service',
      'local expertise',
      'customer satisfaction',
      'proven results',
      'expert guidance',
      'professional insight'
    ];
    
    return genericPhrases.some(phrase => 
      benefit.toLowerCase().includes(phrase.toLowerCase())
    );
  }

  private async generateValuePropositions(brandProfile: BrandProfile, benefits: string[]): Promise<string[]> {
    const { businessName, businessType, location, description } = brandProfile;
    
    const valuePropositions: string[] = [];
    
    // Generate value props based on specific benefits
    benefits.forEach(benefit => {
      if (benefit.includes('24/7') || benefit.includes('round-the-clock')) {
        valuePropositions.push(`Always available when you need us most`);
      }
      
      if (benefit.includes('local') || benefit.includes('community')) {
        valuePropositions.push(`Deep local knowledge and community connections`);
      }
      
      if (benefit.includes('custom') || benefit.includes('personalized')) {
        valuePropositions.push(`Tailored solutions for your unique needs`);
      }
      
      if (benefit.includes('secure') || benefit.includes('safe')) {
        valuePropositions.push(`Your security and privacy are our top priority`);
      }
      
      if (benefit.includes('instant') || benefit.includes('immediate')) {
        valuePropositions.push(`Get results instantly, no waiting`);
      }
      
      if (benefit.includes('free') || benefit.includes('no cost')) {
        valuePropositions.push(`Premium service at no extra cost`);
      }
    });
    
    // Add business-specific value props
    if (description) {
      if (description.toLowerCase().includes('family') || description.toLowerCase().includes('generations')) {
        valuePropositions.push(`Family-owned business with generations of experience`);
      }
      
      if (description.toLowerCase().includes('innovative') || description.toLowerCase().includes('cutting-edge')) {
        valuePropositions.push(`Cutting-edge technology and innovative solutions`);
      }
      
      if (description.toLowerCase().includes('award') || description.toLowerCase().includes('recognized')) {
        valuePropositions.push(`Award-winning service recognized by industry leaders`);
      }
    }
    
    return [...new Set(valuePropositions)].slice(0, 8);
  }

  private async identifyCompetitiveAdvantages(brandProfile: BrandProfile, advantages: string[]): Promise<string[]> {
    const { businessName, businessType, location, description } = brandProfile;
    
    const advantagesList: string[] = [];
    
    // Add provided advantages
    advantagesList.push(...advantages);
    
    // Generate advantages based on business data
    if (description) {
      if (description.toLowerCase().includes('years') || description.toLowerCase().includes('experience')) {
        advantagesList.push(`Years of proven experience in ${businessType}`);
      }
      
      if (description.toLowerCase().includes('local') || description.toLowerCase().includes('community')) {
        advantagesList.push(`Deep local market knowledge and community relationships`);
      }
      
      if (description.toLowerCase().includes('team') || description.toLowerCase().includes('staff')) {
        advantagesList.push(`Highly trained and experienced team`);
      }
    }
    
    // Add location-specific advantages
    if (location.toLowerCase().includes('kenya')) {
      advantagesList.push(`Understanding of Kenyan market dynamics and regulations`);
      advantagesList.push(`Local language support and cultural sensitivity`);
    }
    
    return [...new Set(advantagesList)].slice(0, 6);
  }

  private async identifyTargetPainPoints(businessType: string, services: string[], targetAudience: string): Promise<string[]> {
    const painPoints: string[] = [];
    
    // Business type specific pain points
    const businessTypePainPoints: { [key: string]: string[] } = {
      'Financial Technology Company': [
        'Complex financial processes and paperwork',
        'High fees and hidden charges',
        'Slow transaction processing times',
        'Lack of transparency in financial services',
        'Limited access to financial services',
        'Security concerns with digital payments'
      ],
      'Restaurant': [
        'Long wait times for food',
        'Inconsistent food quality',
        'Limited menu options',
        'Poor customer service',
        'High prices for average food',
        'Inconvenient location or hours'
      ],
      'Retail Store': [
        'Limited product selection',
        'High prices compared to online',
        'Poor customer service',
        'Outdated or damaged products',
        'Inconvenient store hours',
        'Lack of product knowledge from staff'
      ],
      'Healthcare Provider': [
        'Long appointment wait times',
        'High medical costs',
        'Limited availability of specialists',
        'Complex insurance processes',
        'Lack of personalized care',
        'Difficulty accessing medical records'
      ]
    };
    
    const typePainPoints = businessTypePainPoints[businessType] || [];
    painPoints.push(...typePainPoints);
    
    // Service-specific pain points
    services.forEach(service => {
      const serviceLower = service.toLowerCase();
      
      if (serviceLower.includes('delivery')) {
        painPoints.push('Unreliable delivery times and damaged packages');
      }
      
      if (serviceLower.includes('consultation')) {
        painPoints.push('Generic advice that doesn\'t address specific needs');
      }
      
      if (serviceLower.includes('support')) {
        painPoints.push('Poor customer support and slow response times');
      }
    });
    
    return [...new Set(painPoints)].slice(0, 8);
  }

  private async highlightServices(services: string[], businessType: string, location: string): Promise<string[]> {
    return services.map(service => {
      // Add location context to services
      if (location.toLowerCase().includes('kenya')) {
        return `${service} tailored for the Kenyan market`;
      }
      return service;
    }).slice(0, 6);
  }

  private async generateSuccessStories(brandProfile: BrandProfile, benefits: string[]): Promise<string[]> {
    const { businessName, businessType, location } = brandProfile;
    
    const successStories: string[] = [];
    
    // Generate success stories based on benefits
    benefits.forEach(benefit => {
      if (benefit.includes('24/7') || benefit.includes('round-the-clock')) {
        successStories.push(`Customers rely on us for round-the-clock support when they need it most`);
      }
      
      if (benefit.includes('local') || benefit.includes('community')) {
        successStories.push(`Trusted by local businesses and individuals in ${location}`);
      }
      
      if (benefit.includes('custom') || benefit.includes('personalized')) {
        successStories.push(`Helped hundreds of customers with personalized solutions`);
      }
      
      if (benefit.includes('secure') || benefit.includes('safe')) {
        successStories.push(`Zero security incidents - your data is always protected`);
      }
    });
    
    // Add business-specific success stories
    successStories.push(`${businessName} has been serving ${location} with excellence`);
    successStories.push(`Join thousands of satisfied customers who trust ${businessName}`);
    
    return [...new Set(successStories)].slice(0, 6);
  }

  private async identifyBusinessStrengths(brandProfile: BrandProfile, benefits: string[]): Promise<string[]> {
    const { businessName, businessType, location, description } = brandProfile;
    
    const strengths: string[] = [];
    
    // Add benefits as strengths
    strengths.push(...benefits);
    
    // Add description-based strengths
    if (description) {
      if (description.toLowerCase().includes('experience') || description.toLowerCase().includes('years')) {
        strengths.push(`Proven track record and extensive experience`);
      }
      
      if (description.toLowerCase().includes('team') || description.toLowerCase().includes('staff')) {
        strengths.push(`Highly qualified and dedicated team`);
      }
      
      if (description.toLowerCase().includes('technology') || description.toLowerCase().includes('innovative')) {
        strengths.push(`Cutting-edge technology and innovative approaches`);
      }
    }
    
    // Add location-specific strengths
    if (location.toLowerCase().includes('kenya')) {
      strengths.push(`Deep understanding of Kenyan market and culture`);
      strengths.push(`Local language support and cultural sensitivity`);
    }
    
    return [...new Set(strengths)].slice(0, 8);
  }

  private async determineMarketPositioning(brandProfile: BrandProfile, advantages: string[]): Promise<string> {
    const { businessType, location, description } = brandProfile;
    
    let positioning = `Leading ${businessType.toLowerCase()} in ${location}`;
    
    if (description) {
      if (description.toLowerCase().includes('premium') || description.toLowerCase().includes('luxury')) {
        positioning = `Premium ${businessType.toLowerCase()} provider in ${location}`;
      } else if (description.toLowerCase().includes('affordable') || description.toLowerCase().includes('budget')) {
        positioning = `Affordable ${businessType.toLowerCase()} solutions in ${location}`;
      } else if (description.toLowerCase().includes('innovative') || description.toLowerCase().includes('cutting-edge')) {
        positioning = `Innovative ${businessType.toLowerCase()} leader in ${location}`;
      }
    }
    
    return positioning;
  }

  private async identifyContentOpportunities(brandProfile: BrandProfile, benefits: string[]): Promise<string[]> {
    const { businessType, services } = brandProfile;
    
    const opportunities: string[] = [];
    
    // Benefit-based content opportunities
    benefits.forEach(benefit => {
      if (benefit.includes('24/7')) {
        opportunities.push(`Showcase round-the-clock availability and support`);
      }
      
      if (benefit.includes('local')) {
        opportunities.push(`Highlight local community involvement and partnerships`);
      }
      
      if (benefit.includes('custom')) {
        opportunities.push(`Feature customer success stories and case studies`);
      }
      
      if (benefit.includes('secure')) {
        opportunities.push(`Educate about security measures and data protection`);
      }
    });
    
    // Service-based content opportunities
    const servicesList = this.parseTextToList(services);
    servicesList.forEach(service => {
      opportunities.push(`Create educational content about ${service}`);
      opportunities.push(`Showcase ${service} process and benefits`);
    });
    
    return [...new Set(opportunities)].slice(0, 8);
  }

  private async generatePersonalizedMessaging(brandProfile: BrandProfile, benefits: string[]): Promise<{
    headlines: string[];
    subheadlines: string[];
    captions: string[];
    ctas: string[];
  }> {
    const { businessName, businessType, location } = brandProfile;
    
    const headlines: string[] = [];
    const subheadlines: string[] = [];
    const captions: string[] = [];
    const ctas: string[] = [];
    
    // Generate headlines based on specific benefits
    benefits.forEach(benefit => {
      if (benefit.includes('24/7')) {
        headlines.push(`Always Here When You Need Us`);
        headlines.push(`24/7 Support That Never Sleeps`);
      }
      
      if (benefit.includes('local')) {
        headlines.push(`Your Local ${businessType} Experts`);
        headlines.push(`Proudly Serving ${location}`);
      }
      
      if (benefit.includes('custom')) {
        headlines.push(`Made Just for You`);
        headlines.push(`Your Personalized Solution`);
      }
      
      if (benefit.includes('secure')) {
        headlines.push(`Your Security, Our Priority`);
        headlines.push(`Bank-Level Security Guaranteed`);
      }
    });
    
    // Generate subheadlines
    benefits.forEach(benefit => {
      subheadlines.push(`Experience ${benefit} with ${businessName}`);
      subheadlines.push(`Discover why ${benefit} matters for your success`);
    });
    
    // Generate captions
    benefits.forEach(benefit => {
      captions.push(`At ${businessName}, we believe in ${benefit.toLowerCase()}. That's why we've built our entire ${businessType.toLowerCase()} around delivering exactly what you need, when you need it.`);
      captions.push(`Ready to experience ${benefit.toLowerCase()}? ${businessName} is here to make it happen.`);
    });
    
    // Generate CTAs
    benefits.forEach(benefit => {
      ctas.push(`Get ${benefit} Today`);
      ctas.push(`Experience ${benefit} Now`);
      ctas.push(`Start Your ${benefit} Journey`);
    });
    
    return {
      headlines: [...new Set(headlines)].slice(0, 10),
      subheadlines: [...new Set(subheadlines)].slice(0, 10),
      captions: [...new Set(captions)].slice(0, 8),
      ctas: [...new Set(ctas)].slice(0, 8)
    };
  }

  // Method to get specific benefits for content generation
  getSpecificBenefits(brandProfile: BrandProfile): Promise<string[]> {
    return this.analyzeBusiness(brandProfile).then(analysis => analysis.specificBenefits);
  }

  // Method to get personalized messaging
  getPersonalizedMessaging(brandProfile: BrandProfile): Promise<{
    headlines: string[];
    subheadlines: string[];
    captions: string[];
    ctas: string[];
  }> {
    return this.analyzeBusiness(brandProfile).then(analysis => analysis.personalizedMessaging);
  }

  // Method to clear cache (useful for testing or when business data changes)
  clearCache(): void {
    this.analysisCache.clear();
  }
}
