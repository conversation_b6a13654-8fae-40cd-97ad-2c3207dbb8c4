-- Enhanced Business Profiles Table
-- Comprehensive business profile data storage for Revo 1.5 framework

-- Create enhanced_business_profiles table
CREATE TABLE IF NOT EXISTS enhanced_business_profiles (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL,
  
  -- Core company information
  core_info JSONB NOT NULL DEFAULT '{}'::jsonb,
  
  -- Products/Services catalog
  offerings JSONB NOT NULL DEFAULT '[]'::jsonb,
  
  -- Competitive differentiation matrix
  competitive_differentiation JSONB NOT NULL DEFAULT '{}'::jsonb,
  
  -- Trust & authority indicators
  trust_indicators JSONB NOT NULL DEFAULT '{}'::jsonb,
  
  -- Operational parameters
  operational_params JSONB NOT NULL DEFAULT '{}'::jsonb,
  
  -- Content preferences
  content_preferences JSONB NOT NULL DEFAULT '{}'::jsonb,
  
  -- System metadata
  metadata JSONB NOT NULL DEFAULT '{}'::jsonb,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_enhanced_profiles_user_id ON enhanced_business_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_enhanced_profiles_active ON enhanced_business_profiles((metadata->>'isActive'));
CREATE INDEX IF NOT EXISTS idx_enhanced_profiles_business_name ON enhanced_business_profiles((core_info->>'businessName'));
CREATE INDEX IF NOT EXISTS idx_enhanced_profiles_industry ON enhanced_business_profiles((core_info->>'industryCategory'));
CREATE INDEX IF NOT EXISTS idx_enhanced_profiles_location ON enhanced_business_profiles((core_info->'primaryLocation'->>'city'));
CREATE INDEX IF NOT EXISTS idx_enhanced_profiles_completion ON enhanced_business_profiles(((metadata->>'completionScore')::integer));

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_enhanced_profiles_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_enhanced_profiles_updated_at
  BEFORE UPDATE ON enhanced_business_profiles
  FOR EACH ROW
  EXECUTE FUNCTION update_enhanced_profiles_updated_at();

-- Add RLS (Row Level Security) policies
ALTER TABLE enhanced_business_profiles ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only access their own profiles
CREATE POLICY enhanced_profiles_user_access ON enhanced_business_profiles
  FOR ALL USING (auth.uid()::text = user_id);

-- Grant permissions
GRANT ALL ON enhanced_business_profiles TO authenticated;
GRANT ALL ON enhanced_business_profiles TO service_role;

-- Create function to validate profile data
CREATE OR REPLACE FUNCTION validate_enhanced_profile_data()
RETURNS TRIGGER AS $$
BEGIN
  -- Validate core_info
  IF NOT (NEW.core_info ? 'businessName') OR (NEW.core_info->>'businessName') = '' THEN
    RAISE EXCEPTION 'Business name is required';
  END IF;
  
  IF NOT (NEW.core_info ? 'industryCategory') OR (NEW.core_info->>'industryCategory') = '' THEN
    RAISE EXCEPTION 'Industry category is required';
  END IF;
  
  IF NOT (NEW.core_info ? 'primaryLocation') THEN
    RAISE EXCEPTION 'Primary location is required';
  END IF;
  
  -- Validate primary location has city and country
  IF NOT (NEW.core_info->'primaryLocation' ? 'city') OR 
     (NEW.core_info->'primaryLocation'->>'city') = '' THEN
    RAISE EXCEPTION 'Primary location city is required';
  END IF;
  
  IF NOT (NEW.core_info->'primaryLocation' ? 'country') OR 
     (NEW.core_info->'primaryLocation'->>'country') = '' THEN
    RAISE EXCEPTION 'Primary location country is required';
  END IF;
  
  -- Validate offerings is an array
  IF NOT (jsonb_typeof(NEW.offerings) = 'array') THEN
    RAISE EXCEPTION 'Offerings must be an array';
  END IF;
  
  -- Validate metadata
  IF NOT (NEW.metadata ? 'profileId') OR (NEW.metadata->>'profileId') = '' THEN
    RAISE EXCEPTION 'Profile ID is required in metadata';
  END IF;
  
  IF NOT (NEW.metadata ? 'userId') OR (NEW.metadata->>'userId') = '' THEN
    RAISE EXCEPTION 'User ID is required in metadata';
  END IF;
  
  -- Ensure user_id matches metadata.userId
  IF NEW.user_id != (NEW.metadata->>'userId') THEN
    RAISE EXCEPTION 'user_id must match metadata.userId';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create validation trigger
CREATE TRIGGER trigger_validate_enhanced_profile_data
  BEFORE INSERT OR UPDATE ON enhanced_business_profiles
  FOR EACH ROW
  EXECUTE FUNCTION validate_enhanced_profile_data();

-- Create function to calculate completion score
CREATE OR REPLACE FUNCTION calculate_profile_completion_score(profile_data JSONB)
RETURNS INTEGER AS $$
DECLARE
  score INTEGER := 0;
  core_info JSONB;
  offerings JSONB;
  trust_indicators JSONB;
  operational_params JSONB;
  content_preferences JSONB;
BEGIN
  core_info := profile_data->'core_info';
  offerings := profile_data->'offerings';
  trust_indicators := profile_data->'trust_indicators';
  operational_params := profile_data->'operational_params';
  content_preferences := profile_data->'content_preferences';
  
  -- Core info scoring (30 points max)
  IF core_info ? 'businessName' AND (core_info->>'businessName') != '' THEN
    score := score + 5;
  END IF;
  
  IF core_info ? 'industryCategory' AND (core_info->>'industryCategory') != '' THEN
    score := score + 5;
  END IF;
  
  IF core_info ? 'businessModel' AND (core_info->>'businessModel') != '' THEN
    score := score + 5;
  END IF;
  
  IF core_info->'primaryLocation' ? 'city' AND 
     (core_info->'primaryLocation'->>'city') != '' THEN
    score := score + 5;
  END IF;
  
  IF core_info->'primaryLocation' ? 'country' AND 
     (core_info->'primaryLocation'->>'country') != '' THEN
    score := score + 5;
  END IF;
  
  IF core_info ? 'establishmentYear' AND 
     (core_info->>'establishmentYear')::integer > 1900 THEN
    score := score + 5;
  END IF;
  
  -- Offerings scoring (25 points max)
  IF jsonb_array_length(offerings) > 0 THEN
    score := score + 15;
    
    -- Check if any offering has pricing info
    IF EXISTS (
      SELECT 1 FROM jsonb_array_elements(offerings) AS offering
      WHERE offering->'pricingStructure' ? 'basePrice' OR 
            offering->'pricingStructure' ? 'hourlyRate'
    ) THEN
      score := score + 10;
    END IF;
  END IF;
  
  -- Trust indicators scoring (20 points max)
  IF trust_indicators ? 'businessLongevity' AND 
     (trust_indicators->>'businessLongevity')::integer > 0 THEN
    score := score + 5;
  END IF;
  
  IF trust_indicators->'customerBase' ? 'customersServed' AND 
     (trust_indicators->'customerBase'->>'customersServed')::integer > 0 THEN
    score := score + 5;
  END IF;
  
  IF trust_indicators->'reviewScores' ? 'google' AND 
     (trust_indicators->'reviewScores'->>'google')::numeric > 0 THEN
    score := score + 5;
  END IF;
  
  IF trust_indicators->'professionalCredentials' ? 'certifications' AND 
     jsonb_array_length(trust_indicators->'professionalCredentials'->'certifications') > 0 THEN
    score := score + 5;
  END IF;
  
  -- Operational params scoring (15 points max)
  IF operational_params->'contactChannels' ? 'phone' AND 
     (operational_params->'contactChannels'->>'phone') != '' THEN
    score := score + 5;
  END IF;
  
  IF operational_params->'contactChannels' ? 'email' AND 
     (operational_params->'contactChannels'->>'email') != '' THEN
    score := score + 5;
  END IF;
  
  IF operational_params ? 'operatingHours' THEN
    score := score + 5;
  END IF;
  
  -- Content preferences scoring (10 points max)
  IF content_preferences ? 'brandVoice' AND 
     (content_preferences->>'brandVoice') != '' THEN
    score := score + 5;
  END IF;
  
  IF content_preferences ? 'contentThemes' AND 
     jsonb_array_length(content_preferences->'contentThemes') > 0 THEN
    score := score + 5;
  END IF;
  
  RETURN LEAST(100, score);
END;
$$ LANGUAGE plpgsql;

-- Create function to auto-update completion score
CREATE OR REPLACE FUNCTION update_profile_completion_score()
RETURNS TRIGGER AS $$
BEGIN
  NEW.metadata := jsonb_set(
    NEW.metadata,
    '{completionScore}',
    calculate_profile_completion_score(
      jsonb_build_object(
        'core_info', NEW.core_info,
        'offerings', NEW.offerings,
        'trust_indicators', NEW.trust_indicators,
        'operational_params', NEW.operational_params,
        'content_preferences', NEW.content_preferences
      )
    )::text::jsonb
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-update completion score
CREATE TRIGGER trigger_update_completion_score
  BEFORE INSERT OR UPDATE ON enhanced_business_profiles
  FOR EACH ROW
  EXECUTE FUNCTION update_profile_completion_score();

-- Create view for profile analytics
CREATE OR REPLACE VIEW enhanced_profile_analytics AS
SELECT 
  user_id,
  COUNT(*) as total_profiles,
  AVG((metadata->>'completionScore')::integer) as avg_completion_score,
  COUNT(*) FILTER (WHERE (metadata->>'isActive')::boolean = true) as active_profiles,
  COUNT(*) FILTER (WHERE (metadata->>'completionScore')::integer >= 80) as high_quality_profiles,
  array_agg(DISTINCT core_info->>'industryCategory') as industries,
  array_agg(DISTINCT core_info->'primaryLocation'->>'country') as countries
FROM enhanced_business_profiles
GROUP BY user_id;

-- Grant access to the view
GRANT SELECT ON enhanced_profile_analytics TO authenticated;
GRANT SELECT ON enhanced_profile_analytics TO service_role;
