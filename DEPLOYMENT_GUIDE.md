# Revo 1.5 Enhanced Business Profile Framework - Deployment Guide

## 🚀 Quick Start Deployment

### Prerequisites
- Supabase project configured
- Next.js application running
- User authentication system in place

### Step 1: Database Setup
```sql
-- Run the migration file
\i src/lib/supabase/migrations/create-enhanced-business-profiles.sql
```

### Step 2: Environment Variables
Ensure these are set in your `.env.local`:
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### Step 3: Install Dependencies
```bash
npm install
# or
yarn install
```

### Step 4: Integration Points

#### A. Add Enhanced Brand Provider
Wrap your app with the enhanced brand provider:

```tsx
// In your layout.tsx or _app.tsx
import { EnhancedBrandProvider } from '@/contexts/enhanced-brand-context';

export default function Layout({ children }) {
  return (
    <EnhancedBrandProvider>
      {children}
    </EnhancedBrandProvider>
  );
}
```

#### B. Add Navigation Link
Add link to enhanced profiles in your navigation:

```tsx
// In your navigation component
<Link href="/enhanced-profiles">
  Enhanced Profiles
</Link>
```

#### C. Integrate with Existing Content Generation
Update your existing content generation to use enhanced profiles:

```tsx
// In your content generation components
import { EnhancedContentGenerator } from '@/components/content-generation/enhanced-content-generator';

// Replace existing content generator with:
<EnhancedContentGenerator 
  onContentGenerated={handleContentGenerated}
  defaultPlatform="instagram"
/>
```

### Step 5: Test the Integration
```bash
# Run tests
npm test src/tests/enhanced-profiles-integration.test.ts

# Start development server
npm run dev
```

## 📋 Deployment Checklist

### Database
- [ ] Enhanced business profiles table created
- [ ] Validation triggers installed
- [ ] RLS policies configured
- [ ] Analytics view created

### API Endpoints
- [ ] `/api/enhanced-business-profiles` - CRUD operations
- [ ] `/api/enhanced-content-generation` - Content generation
- [ ] Authentication middleware working
- [ ] Error handling implemented

### UI Components
- [ ] Enhanced Profile Form working
- [ ] Profile Manager functional
- [ ] Content Generator integrated
- [ ] Analytics Dashboard displaying data
- [ ] Migration Tool operational

### Integration
- [ ] Enhanced Brand Context providing data
- [ ] Legacy profile migration working
- [ ] Content generation using enhanced profiles
- [ ] Quality validation functioning
- [ ] Localization engine active

## 🔧 Configuration Options

### Localization Settings
Configure supported regions in `src/ai/localization-engine.ts`:

```typescript
// Add new regions
const REGIONAL_PROFILES = {
  'Your Country': {
    localPhrases: ['phrase1', 'phrase2'],
    culturalReferences: ['reference1'],
    businessEtiquette: ['etiquette1']
  }
};
```

### Content Quality Standards
Adjust quality thresholds in `src/ai/content-quality-validator.ts`:

```typescript
// Modify scoring thresholds
const QUALITY_THRESHOLDS = {
  MINIMUM_COMPLETION_SCORE: 50,
  HIGH_QUALITY_SCORE: 85,
  SPECIFICITY_THRESHOLD: 70
};
```

### Business Profile Validation
Customize validation rules in `src/lib/supabase/services/enhanced-business-profile-service.ts`:

```typescript
// Add custom validation rules
validateProfile(profile: EnhancedBusinessProfile) {
  // Your custom validation logic
}
```

## 🎯 Usage Examples

### Creating an Enhanced Profile
```tsx
import { useEnhancedBrand } from '@/contexts/enhanced-brand-context';

function CreateProfile() {
  const { saveEnhancedProfile } = useEnhancedBrand();
  
  const handleSave = async (profile) => {
    try {
      const profileId = await saveEnhancedProfile(profile);
      console.log('Profile created:', profileId);
    } catch (error) {
      console.error('Error:', error);
    }
  };
  
  return <EnhancedProfileForm onSave={handleSave} />;
}
```

### Generating Enhanced Content
```tsx
import { EnhancedContentGenerator } from '@/components/content-generation/enhanced-content-generator';

function ContentPage() {
  const handleContentGenerated = (content) => {
    console.log('Generated content:', content);
    // Use the content in your application
  };
  
  return (
    <EnhancedContentGenerator 
      onContentGenerated={handleContentGenerated}
      defaultPlatform="instagram"
    />
  );
}
```

### Migrating Legacy Profiles
```tsx
import { ProfileMigrationTool } from '@/components/migration/profile-migration-tool';

function MigrationPage() {
  const handleMigrationComplete = (enhancedProfile) => {
    console.log('Migration complete:', enhancedProfile);
    // Redirect to profile management or show success
  };
  
  return (
    <ProfileMigrationTool 
      onMigrationComplete={handleMigrationComplete}
    />
  );
}
```

## 🔍 Monitoring & Analytics

### Profile Completion Tracking
Monitor profile completion scores:

```tsx
import { useEnhancedBrand } from '@/contexts/enhanced-brand-context';

function ProfileStats() {
  const { enhancedProfiles, getProfileCompletionScore } = useEnhancedBrand();
  
  const averageCompletion = enhancedProfiles.reduce((sum, profile) => 
    sum + getProfileCompletionScore(profile), 0
  ) / enhancedProfiles.length;
  
  return <div>Average Completion: {averageCompletion}%</div>;
}
```

### Content Quality Metrics
Track content generation quality:

```tsx
// In your content generation handler
const handleContentGenerated = (content) => {
  // Log quality metrics
  console.log('Quality Score:', content.qualityScore);
  console.log('Localization Applied:', content.localizationApplied);
  console.log('Data Sources:', content.dataSourcesUsed);
  
  // Send to analytics service
  analytics.track('content_generated', {
    quality_score: content.qualityScore,
    localization: content.localizationApplied,
    platform: 'instagram'
  });
};
```

## 🐛 Troubleshooting

### Common Issues

#### 1. Database Connection Issues
```bash
# Check Supabase connection
npx supabase status
```

#### 2. Profile Not Saving
- Verify user authentication
- Check RLS policies
- Validate profile data structure

#### 3. Content Generation Failing
- Ensure profile completion > 50%
- Check API key configuration
- Verify business profile data

#### 4. Localization Not Working
- Confirm localization toggle enabled
- Check country/region support
- Verify localization engine configuration

### Debug Mode
Enable debug logging:

```typescript
// In your environment variables
DEBUG_ENHANCED_PROFILES=true

// In your code
if (process.env.DEBUG_ENHANCED_PROFILES) {
  console.log('Debug info:', data);
}
```

## 📈 Performance Optimization

### Database Optimization
- Index frequently queried fields
- Use JSONB efficiently
- Implement proper caching

### UI Performance
- Lazy load components
- Implement virtual scrolling for large lists
- Use React.memo for expensive components

### API Optimization
- Implement request caching
- Use pagination for large datasets
- Optimize database queries

## 🔒 Security Considerations

### Data Protection
- All profile data protected by RLS
- User can only access their own profiles
- Sensitive data encrypted at rest

### API Security
- Authentication required for all endpoints
- Input validation on all requests
- Rate limiting implemented

### Privacy Compliance
- User data deletion supported
- Data export functionality available
- Privacy policy compliance built-in

## 📚 Additional Resources

### Documentation
- [Enhanced Business Profile Types](src/lib/types/enhanced-business-profile.ts)
- [Content Generation Framework](src/ai/revo-1.5-enhanced-content-framework.ts)
- [Localization Engine](src/ai/localization-engine.ts)
- [Quality Validator](src/ai/content-quality-validator.ts)

### Support
- Check the test files for usage examples
- Review component props and interfaces
- Refer to the implementation summary document

---

## ✅ Deployment Complete!

Your Revo 1.5 Enhanced Business Profile Framework is now ready for production use. The system provides:

- **Comprehensive Business Profiles** with 85%+ completion tracking
- **Advanced Content Generation** with quality validation
- **Localization Support** for multiple regions
- **Migration Tools** for legacy profile upgrades
- **Analytics Dashboard** for performance monitoring
- **Production-Ready** with comprehensive testing

For additional support or customization, refer to the implementation documentation and test files provided.
