'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Sparkles, CheckCircle, AlertCircle } from 'lucide-react';

export default function TestEnhancedIntegration() {
  const [testResults, setTestResults] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [migrationStatus, setMigrationStatus] = useState<any>(null);
  const [isMigrating, setIsMigrating] = useState(false);

  const runIntegrationTest = async () => {
    setIsLoading(true);
    setTestResults(null);

    try {
      console.log('🧪 Testing Enhanced Content Generation Integration...');

      // Test 1: Check if enhanced API endpoint exists
      const apiTest = await fetch('/api/enhanced-content-generation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          profileId: 'test-profile-id',
          platform: 'instagram',
          contentType: 'promotional'
        })
      });

      const apiResult = await apiTest.json();

      // Test 2: Check if legacy API still works
      const legacyTest = await fetch('/api/social-media-expert', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'generate-posts',
          businessProfile: {
            businessName: 'Test Business',
            businessType: 'Services',
            location: 'New York, NY'
          },
          platform: 'instagram',
          count: 1
        })
      });

      const legacyResult = await legacyTest.json();

      setTestResults({
        enhancedAPI: {
          status: apiTest.status,
          response: apiResult,
          working: apiTest.status !== 500
        },
        legacyAPI: {
          status: legacyTest.status,
          response: legacyResult,
          working: legacyTest.ok && legacyResult.success
        },
        integration: {
          enhancedFirst: true,
          fallbackWorking: legacyTest.ok
        }
      });

    } catch (error) {
      console.error('❌ Integration test failed:', error);
      setTestResults({
        error: error.message,
        enhancedAPI: { working: false },
        legacyAPI: { working: false }
      });
    } finally {
      setIsLoading(false);
    }
  };

  const runMigration = async () => {
    setIsMigrating(true);
    setMigrationStatus(null);

    try {
      console.log('🔄 Running database migration...');

      const response = await fetch('/api/migrate-enhanced-profiles', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      const result = await response.json();
      setMigrationStatus(result);

    } catch (error) {
      console.error('❌ Migration failed:', error);
      setMigrationStatus({
        success: false,
        error: error.message
      });
    } finally {
      setIsMigrating(false);
    }
  };

  const checkTableStatus = async () => {
    try {
      const response = await fetch('/api/migrate-enhanced-profiles');
      const result = await response.json();
      setMigrationStatus(result);
    } catch (error) {
      console.error('❌ Table status check failed:', error);
    }
  };

  return (
    <div className="container mx-auto py-8 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5" />
            Enhanced Content Generation Integration Test
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-muted-foreground">
            This test verifies that the enhanced content generation framework is properly integrated
            and falls back to the legacy system when needed.
          </p>

          <div className="space-y-3">
            <Button
              onClick={checkTableStatus}
              variant="outline"
              className="w-full"
            >
              Check Database Status
            </Button>

            <Button
              onClick={runMigration}
              disabled={isMigrating}
              variant="secondary"
              className="w-full"
            >
              {isMigrating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Running Migration...
                </>
              ) : (
                'Run Database Migration'
              )}
            </Button>

            <Button
              onClick={runIntegrationTest}
              disabled={isLoading}
              className="w-full"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Running Integration Test...
                </>
              ) : (
                <>
                  <Sparkles className="h-4 w-4 mr-2" />
                  Run Integration Test
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {migrationStatus && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {migrationStatus.success ? (
                <CheckCircle className="h-5 w-5 text-green-600" />
              ) : (
                <AlertCircle className="h-5 w-5 text-red-600" />
              )}
              Database Migration Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Badge variant={migrationStatus.success ? "default" : "destructive"}>
                  {migrationStatus.success ? "Success" : "Failed"}
                </Badge>
                {migrationStatus.tableExists !== undefined && (
                  <Badge variant={migrationStatus.tableExists ? "default" : "secondary"}>
                    Table: {migrationStatus.tableExists ? "Exists" : "Missing"}
                  </Badge>
                )}
              </div>
              <pre className="bg-muted p-3 rounded text-xs overflow-auto max-h-40">
                {JSON.stringify(migrationStatus, null, 2)}
              </pre>
            </div>
          </CardContent>
        </Card>
      )}

      {testResults && (
        <div className="space-y-4">
          {/* Enhanced API Test Results */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {testResults.enhancedAPI?.working ? (
                  <CheckCircle className="h-5 w-5 text-green-600" />
                ) : (
                  <AlertCircle className="h-5 w-5 text-red-600" />
                )}
                Enhanced API Test
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Badge variant={testResults.enhancedAPI?.working ? "default" : "destructive"}>
                    Status: {testResults.enhancedAPI?.status || 'Error'}
                  </Badge>
                </div>
                <pre className="bg-muted p-3 rounded text-xs overflow-auto max-h-40">
                  {JSON.stringify(testResults.enhancedAPI?.response, null, 2)}
                </pre>
              </div>
            </CardContent>
          </Card>

          {/* Legacy API Test Results */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {testResults.legacyAPI?.working ? (
                  <CheckCircle className="h-5 w-5 text-green-600" />
                ) : (
                  <AlertCircle className="h-5 w-5 text-red-600" />
                )}
                Legacy API Test (Fallback)
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Badge variant={testResults.legacyAPI?.working ? "default" : "destructive"}>
                    Status: {testResults.legacyAPI?.status || 'Error'}
                  </Badge>
                </div>
                <pre className="bg-muted p-3 rounded text-xs overflow-auto max-h-40">
                  {JSON.stringify(testResults.legacyAPI?.response, null, 2)}
                </pre>
              </div>
            </CardContent>
          </Card>

          {/* Integration Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Integration Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <span className="font-medium">Enhanced Framework:</span>
                  <Badge variant={testResults.enhancedAPI?.working ? "default" : "secondary"}>
                    {testResults.enhancedAPI?.working ? "Active" : "Not Available"}
                  </Badge>
                </div>
                <div className="flex items-center gap-2">
                  <span className="font-medium">Fallback System:</span>
                  <Badge variant={testResults.legacyAPI?.working ? "default" : "destructive"}>
                    {testResults.legacyAPI?.working ? "Working" : "Failed"}
                  </Badge>
                </div>
                <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded">
                  <p className="text-sm text-blue-700">
                    <strong>Expected Behavior:</strong> The system should try the enhanced framework first,
                    then fall back to the legacy system if enhanced profiles are not available or incomplete.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
