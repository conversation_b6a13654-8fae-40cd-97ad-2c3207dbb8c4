/**
 * Content Memory System - Tracks generated content to prevent repetition and improve quality
 * Implements intelligent content tracking, pattern detection, and learning from performance
 */

export interface ContentMemory {
  id: string;
  businessName: string;
  businessType: string;
  location: string;
  approach: string;
  headline: string;
  subheadline: string;
  caption: string;
  cta: string;
  timestamp: number;
  performance?: {
    engagement?: number;
    clicks?: number;
    conversions?: number;
    shares?: number;
    comments?: number;
  };
  quality?: {
    uniqueness: number;
    creativity: number;
    relevance: number;
    emotionalImpact: number;
  };
  patterns?: {
    wordCount: number;
    emotionalTone: string;
    psychologicalTriggers: string[];
    keyPhrases: string[];
  };
}

export interface MemoryConfig {
  maxMemorySize: number;
  retentionDays: number;
  similarityThreshold: number;
  performanceWeight: number;
}

export interface ContentSimilarity {
  content: ContentMemory;
  similarity: number;
  matchingElements: string[];
}

export interface MemoryInsights {
  topPerformingApproaches: Array<{ approach: string; avgPerformance: number }>;
  overusedPhrases: Array<{ phrase: string; count: number }>;
  successfulPatterns: Array<{ pattern: string; successRate: number }>;
  recommendations: string[];
}

export class ContentMemorySystem {
  private memory: ContentMemory[] = [];
  private config: MemoryConfig;
  private phraseFrequency: Map<string, number> = new Map();
  private patternSuccess: Map<string, number> = new Map();

  constructor(config: Partial<MemoryConfig> = {}) {
    this.config = {
      maxMemorySize: 1000,
      retentionDays: 30,
      similarityThreshold: 0.7,
      performanceWeight: 0.8,
      ...config
    };
  }

  /**
   * Store content in memory
   */
  storeContent(content: Omit<ContentMemory, 'id' | 'timestamp' | 'patterns'>): string {
    const contentId = this.generateContentId();
    const timestamp = Date.now();
    
    const contentMemory: ContentMemory = {
      ...content,
      id: contentId,
      timestamp,
      patterns: this.analyzeContentPatterns(content)
    };

    this.memory.push(contentMemory);
    
    // Update phrase frequency
    this.updatePhraseFrequency(contentMemory);
    
    // Clean up old memory
    this.cleanupMemory();
    
    return contentId;
  }

  /**
   * Analyze content patterns
   */
  private analyzeContentPatterns(content: Omit<ContentMemory, 'id' | 'timestamp' | 'patterns'>): ContentMemory['patterns'] {
    const allText = `${content.headline} ${content.subheadline} ${content.caption} ${content.cta}`;
    const words = allText.toLowerCase().split(/\s+/);
    
    return {
      wordCount: words.length,
      emotionalTone: this.detectEmotionalTone(allText),
      psychologicalTriggers: this.detectPsychologicalTriggers(allText),
      keyPhrases: this.extractKeyPhrases(allText)
    };
  }

  /**
   * Detect emotional tone of content
   */
  private detectEmotionalTone(text: string): string {
    const positiveWords = ['amazing', 'incredible', 'outstanding', 'excellent', 'fantastic', 'wonderful', 'great', 'awesome'];
    const negativeWords = ['terrible', 'awful', 'horrible', 'bad', 'disappointing', 'frustrating', 'annoying'];
    const urgentWords = ['urgent', 'hurry', 'limited', 'quick', 'fast', 'immediate', 'now', 'today'];
    const excitedWords = ['exciting', 'thrilling', 'amazing', 'incredible', 'wow', 'fantastic', 'awesome'];

    const textLower = text.toLowerCase();
    
    const positiveCount = positiveWords.filter(word => textLower.includes(word)).length;
    const negativeCount = negativeWords.filter(word => textLower.includes(word)).length;
    const urgentCount = urgentWords.filter(word => textLower.includes(word)).length;
    const excitedCount = excitedWords.filter(word => textLower.includes(word)).length;

    if (excitedCount > 2) return 'excited';
    if (urgentCount > 2) return 'urgent';
    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  }

  /**
   * Detect psychological triggers in content
   */
  private detectPsychologicalTriggers(text: string): string[] {
    const triggers: Record<string, string[]> = {
      scarcity: ['limited', 'only', 'few', 'last', 'exclusive', 'rare', 'unique'],
      urgency: ['now', 'today', 'hurry', 'quick', 'immediate', 'urgent', 'deadline'],
      socialProof: ['customers', 'clients', 'people', 'everyone', 'community', 'join', 'others'],
      authority: ['expert', 'professional', 'certified', 'proven', 'tested', 'guaranteed'],
      reciprocity: ['free', 'gift', 'bonus', 'special', 'exclusive', 'complimentary'],
      commitment: ['promise', 'guarantee', 'commit', 'pledge', 'assure', 'vow']
    };

    const detectedTriggers: string[] = [];
    const textLower = text.toLowerCase();

    Object.entries(triggers).forEach(([trigger, words]) => {
      if (words.some(word => textLower.includes(word))) {
        detectedTriggers.push(trigger);
      }
    });

    return detectedTriggers;
  }

  /**
   * Extract key phrases from content
   */
  private extractKeyPhrases(text: string): string[] {
    // Simple phrase extraction (in production, use more sophisticated NLP)
    const phrases = text.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 3)
      .slice(0, 10); // Top 10 words

    return phrases;
  }

  /**
   * Update phrase frequency tracking
   */
  private updatePhraseFrequency(content: ContentMemory): void {
    if (!content.patterns) return;

    content.patterns.keyPhrases.forEach(phrase => {
      const count = this.phraseFrequency.get(phrase) || 0;
      this.phraseFrequency.set(phrase, count + 1);
    });
  }

  /**
   * Check for content similarity
   */
  checkSimilarity(newContent: Partial<ContentMemory>): ContentSimilarity[] {
    const similarities: ContentSimilarity[] = [];
    const recentContent = this.getRecentContent(this.config.retentionDays);

    recentContent.forEach(existingContent => {
      const similarity = this.calculateSimilarity(newContent, existingContent);
      
      if (similarity > this.config.similarityThreshold) {
        similarities.push({
          content: existingContent,
          similarity,
          matchingElements: this.findMatchingElements(newContent, existingContent)
        });
      }
    });

    return similarities.sort((a, b) => b.similarity - a.similarity);
  }

  /**
   * Calculate content similarity
   */
  private calculateSimilarity(content1: Partial<ContentMemory>, content2: ContentMemory): number {
    let totalSimilarity = 0;
    let elementCount = 0;

    // Compare headlines
    if (content1.headline && content2.headline) {
      totalSimilarity += this.textSimilarity(content1.headline, content2.headline);
      elementCount++;
    }

    // Compare subheadlines
    if (content1.subheadline && content2.subheadline) {
      totalSimilarity += this.textSimilarity(content1.subheadline, content2.subheadline);
      elementCount++;
    }

    // Compare captions
    if (content1.caption && content2.caption) {
      totalSimilarity += this.textSimilarity(content1.caption, content2.caption);
      elementCount++;
    }

    // Compare CTAs
    if (content1.cta && content2.cta) {
      totalSimilarity += this.textSimilarity(content1.cta, content2.cta);
      elementCount++;
    }

    return elementCount > 0 ? totalSimilarity / elementCount : 0;
  }

  /**
   * Calculate text similarity using word overlap
   */
  private textSimilarity(text1: string, text2: string): number {
    const words1 = text1.toLowerCase().split(/\s+/);
    const words2 = text2.toLowerCase().split(/\s+/);
    
    const intersection = words1.filter(word => words2.includes(word));
    const union = [...new Set([...words1, ...words2])];
    
    return union.length > 0 ? intersection.length / union.length : 0;
  }

  /**
   * Find matching elements between content
   */
  private findMatchingElements(content1: Partial<ContentMemory>, content2: ContentMemory): string[] {
    const matching: string[] = [];

    if (content1.headline && content2.headline && this.textSimilarity(content1.headline, content2.headline) > 0.5) {
      matching.push('headline');
    }
    if (content1.subheadline && content2.subheadline && this.textSimilarity(content1.subheadline, content2.subheadline) > 0.5) {
      matching.push('subheadline');
    }
    if (content1.caption && content2.caption && this.textSimilarity(content1.caption, content2.caption) > 0.5) {
      matching.push('caption');
    }
    if (content1.cta && content2.cta && this.textSimilarity(content1.cta, content2.cta) > 0.5) {
      matching.push('cta');
    }

    return matching;
  }

  /**
   * Update content performance
   */
  updatePerformance(contentId: string, performance: ContentMemory['performance']): void {
    const content = this.memory.find(c => c.id === contentId);
    if (content) {
      content.performance = { ...content.performance, ...performance };
      
      // Update pattern success rates
      if (content.patterns) {
        content.patterns.psychologicalTriggers.forEach(trigger => {
          const currentSuccess = this.patternSuccess.get(trigger) || 0;
          const performanceScore = this.calculatePerformanceScore(performance);
          this.patternSuccess.set(trigger, (currentSuccess + performanceScore) / 2);
        });
      }
    }
  }

  /**
   * Calculate performance score from metrics
   */
  private calculatePerformanceScore(performance: ContentMemory['performance']): number {
    if (!performance) return 0;

    const weights = {
      engagement: 0.3,
      clicks: 0.2,
      conversions: 0.3,
      shares: 0.1,
      comments: 0.1
    };

    let score = 0;
    let totalWeight = 0;

    Object.entries(weights).forEach(([metric, weight]) => {
      const value = performance[metric as keyof typeof performance] || 0;
      score += value * weight;
      totalWeight += weight;
    });

    return totalWeight > 0 ? score / totalWeight : 0;
  }

  /**
   * Get content insights and recommendations
   */
  getInsights(): MemoryInsights {
    const recentContent = this.getRecentContent(this.config.retentionDays);
    
    // Top performing approaches
    const approachPerformance = new Map<string, number[]>();
    recentContent.forEach(content => {
      if (content.performance) {
        const score = this.calculatePerformanceScore(content.performance);
        const current = approachPerformance.get(content.approach) || [];
        current.push(score);
        approachPerformance.set(content.approach, current);
      }
    });

    const topPerformingApproaches = Array.from(approachPerformance.entries())
      .map(([approach, scores]) => ({
        approach,
        avgPerformance: scores.reduce((a, b) => a + b, 0) / scores.length
      }))
      .sort((a, b) => b.avgPerformance - a.avgPerformance)
      .slice(0, 5);

    // Overused phrases
    const overusedPhrases = Array.from(this.phraseFrequency.entries())
      .filter(([, count]) => count > 3)
      .map(([phrase, count]) => ({ phrase, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Successful patterns
    const successfulPatterns = Array.from(this.patternSuccess.entries())
      .map(([pattern, successRate]) => ({ pattern, successRate }))
      .sort((a, b) => b.successRate - a.successRate)
      .slice(0, 10);

    // Generate recommendations
    const recommendations = this.generateRecommendations(topPerformingApproaches, overusedPhrases, successfulPatterns);

    return {
      topPerformingApproaches,
      overusedPhrases,
      successfulPatterns,
      recommendations
    };
  }

  /**
   * Generate recommendations based on insights
   */
  private generateRecommendations(
    topApproaches: Array<{ approach: string; avgPerformance: number }>,
    overusedPhrases: Array<{ phrase: string; count: number }>,
    successfulPatterns: Array<{ pattern: string; successRate: number }>
  ): string[] {
    const recommendations: string[] = [];

    // Approach recommendations
    if (topApproaches.length > 0) {
      const bestApproach = topApproaches[0];
      recommendations.push(`Consider using the "${bestApproach.approach}" approach more often (avg performance: ${bestApproach.avgPerformance.toFixed(2)})`);
    }

    // Phrase recommendations
    if (overusedPhrases.length > 0) {
      const mostOverused = overusedPhrases[0];
      recommendations.push(`Avoid overusing "${mostOverused.phrase}" (used ${mostOverused.count} times). Try creative alternatives.`);
    }

    // Pattern recommendations
    if (successfulPatterns.length > 0) {
      const bestPattern = successfulPatterns[0];
      recommendations.push(`The "${bestPattern.pattern}" psychological trigger is performing well (success rate: ${(bestPattern.successRate * 100).toFixed(1)}%). Use it more strategically.`);
    }

    // General recommendations
    recommendations.push('Focus on creating unique, business-specific content that stands out from previous generations.');
    recommendations.push('Use data-driven insights to optimize content performance and reduce repetition.');

    return recommendations;
  }

  /**
   * Get recent content
   */
  private getRecentContent(days: number): ContentMemory[] {
    const cutoffTime = Date.now() - (days * 24 * 60 * 60 * 1000);
    return this.memory.filter(content => content.timestamp > cutoffTime);
  }

  /**
   * Clean up old memory
   */
  private cleanupMemory(): void {
    // Remove old content
    const cutoffTime = Date.now() - (this.config.retentionDays * 24 * 60 * 60 * 1000);
    this.memory = this.memory.filter(content => content.timestamp > cutoffTime);

    // Limit memory size
    if (this.memory.length > this.config.maxMemorySize) {
      this.memory.sort((a, b) => b.timestamp - a.timestamp);
      this.memory = this.memory.slice(0, this.config.maxMemorySize);
    }
  }

  /**
   * Generate unique content ID
   */
  private generateContentId(): string {
    return `content_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get memory statistics
   */
  getMemoryStats(): {
    totalContent: number;
    recentContent: number;
    averagePerformance: number;
    topPhrases: Array<{ phrase: string; count: number }>;
    memoryUsage: number;
  } {
    const recentContent = this.getRecentContent(this.config.retentionDays);
    const performanceScores = recentContent
      .filter(c => c.performance)
      .map(c => this.calculatePerformanceScore(c.performance!));
    
    const averagePerformance = performanceScores.length > 0 
      ? performanceScores.reduce((a, b) => a + b, 0) / performanceScores.length 
      : 0;

    const topPhrases = Array.from(this.phraseFrequency.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([phrase, count]) => ({ phrase, count }));

    return {
      totalContent: this.memory.length,
      recentContent: recentContent.length,
      averagePerformance,
      topPhrases,
      memoryUsage: (this.memory.length / this.config.maxMemorySize) * 100
    };
  }

  /**
   * Clear all memory
   */
  clearMemory(): void {
    this.memory = [];
    this.phraseFrequency.clear();
    this.patternSuccess.clear();
  }

  /**
   * Export memory data
   */
  exportMemory(): ContentMemory[] {
    return [...this.memory];
  }

  /**
   * Import memory data
   */
  importMemory(data: ContentMemory[]): void {
    this.memory = [...data];
    
    // Rebuild phrase frequency
    this.phraseFrequency.clear();
    this.memory.forEach(content => {
      if (content.patterns) {
        content.patterns.keyPhrases.forEach(phrase => {
          const count = this.phraseFrequency.get(phrase) || 0;
          this.phraseFrequency.set(phrase, count + 1);
        });
      }
    });
  }
}

// Export singleton instance
export const contentMemorySystem = new ContentMemorySystem();

export default ContentMemorySystem;

