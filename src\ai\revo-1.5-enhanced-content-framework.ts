/**
 * Revo 1.5 Enhanced Content Generation Framework
 * Integrates comprehensive business profile data with RSS feeds, trending topics, and local market data
 */

import { EnhancedBusinessProfile, ContentGenerationContext, Revo15ContentStructure, ContentQualityStandards } from '@/lib/types/enhanced-business-profile';
import { rssService } from '@/services/rss-feed-service';
import { trendingEnhancer } from './trending-content-enhancer';
import { regionalEngine } from './regional-communication-engine';

export class Revo15EnhancedContentFramework {
  private qualityStandards: ContentQualityStandards = {
    specificityRequirement: true,
    authenticityCheck: true,
    relevanceFilter: true,
    completenessValidation: true,
    toneAdaptation: true,
    problemSolutionFocus: true
  };

  /**
   * Generate enhanced content using comprehensive business profile data
   */
  async generateEnhancedContent(context: ContentGenerationContext): Promise<{
    success: boolean;
    content?: Revo15ContentStructure;
    error?: string;
    qualityScore: number;
    dataIntegration: {
      businessProfileUsed: boolean;
      rssDataIntegrated: boolean;
      trendingTopicsUsed: boolean;
      localMarketDataUsed: boolean;
      localizationApplied: boolean;
    };
  }> {
    try {
      // Step 1: Validate business profile completeness
      const profileValidation = this.validateBusinessProfile(context.businessProfile);
      if (!profileValidation.isComplete) {
        return {
          success: false,
          error: `Missing critical business profile data: ${profileValidation.missingFields.join(', ')}`,
          qualityScore: 0,
          dataIntegration: {
            businessProfileUsed: false,
            rssDataIntegrated: false,
            trendingTopicsUsed: false,
            localMarketDataUsed: false,
            localizationApplied: false
          }
        };
      }

      // Step 2: Gather external data sources
      const dataIntegration = await this.gatherDataSources(context);

      // Step 3: Generate content structure
      const contentStructure = await this.generateContentStructure(context, dataIntegration);

      // Step 4: Apply quality standards
      const qualityScore = this.calculateQualityScore(contentStructure, context.businessProfile);

      // Step 5: Apply localization if enabled
      if (context.businessProfile.contentPreferences.localizationToggle) {
        await this.applyLocalization(contentStructure, context.businessProfile.coreInfo.primaryLocation);
        dataIntegration.localizationApplied = true;
      }

      return {
        success: true,
        content: contentStructure,
        qualityScore,
        dataIntegration
      };

    } catch (error) {
      console.error('❌ [Revo 1.5 Framework] Content generation failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        qualityScore: 0,
        dataIntegration: {
          businessProfileUsed: false,
          rssDataIntegrated: false,
          trendingTopicsUsed: false,
          localMarketDataUsed: false,
          localizationApplied: false
        }
      };
    }
  }

  /**
   * Validate business profile completeness
   */
  private validateBusinessProfile(profile: EnhancedBusinessProfile): {
    isComplete: boolean;
    missingFields: string[];
    completionScore: number;
  } {
    const requiredFields = [
      'coreInfo.businessName',
      'coreInfo.industryCategory',
      'coreInfo.primaryLocation.city',
      'coreInfo.primaryLocation.country',
      'offerings',
      'operationalParams.contactChannels'
    ];

    const missingFields: string[] = [];
    
    // Check core info
    if (!profile.coreInfo?.businessName) missingFields.push('Business Name');
    if (!profile.coreInfo?.industryCategory) missingFields.push('Industry Category');
    if (!profile.coreInfo?.primaryLocation?.city) missingFields.push('Primary Location City');
    if (!profile.coreInfo?.primaryLocation?.country) missingFields.push('Primary Location Country');
    
    // Check offerings
    if (!profile.offerings || profile.offerings.length === 0) missingFields.push('Products/Services');
    
    // Check contact channels
    if (!profile.operationalParams?.contactChannels) missingFields.push('Contact Information');

    const completionScore = Math.max(0, 100 - (missingFields.length * 15));
    
    return {
      isComplete: missingFields.length === 0,
      missingFields,
      completionScore
    };
  }

  /**
   * Gather data from external sources
   */
  private async gatherDataSources(context: ContentGenerationContext): Promise<{
    businessProfileUsed: boolean;
    rssDataIntegrated: boolean;
    trendingTopicsUsed: boolean;
    localMarketDataUsed: boolean;
    localizationApplied: boolean;
    rssData?: any;
    trendingData?: any;
    localData?: any;
  }> {
    const integration = {
      businessProfileUsed: true,
      rssDataIntegrated: false,
      trendingTopicsUsed: false,
      localMarketDataUsed: false,
      localizationApplied: false,
      rssData: null,
      trendingData: null,
      localData: null
    };

    try {
      // Gather RSS feed data
      const businessType = context.businessProfile.coreInfo.industryCategory.toLowerCase();
      const rssData = await rssService.getTrendingData(businessType as any, 20);
      if (rssData && rssData.articles.length > 0) {
        integration.rssData = rssData;
        integration.rssDataIntegrated = true;
      }
    } catch (error) {
      console.warn('⚠️ [Revo 1.5] RSS data unavailable:', error);
    }

    try {
      // Gather trending topics
      const trendingData = await trendingEnhancer.getTrendingEnhancement({
        businessType: context.businessProfile.coreInfo.industryCategory,
        location: `${context.businessProfile.coreInfo.primaryLocation.city}, ${context.businessProfile.coreInfo.primaryLocation.country}`
      });
      if (trendingData) {
        integration.trendingData = trendingData;
        integration.trendingTopicsUsed = true;
      }
    } catch (error) {
      console.warn('⚠️ [Revo 1.5] Trending data unavailable:', error);
    }

    try {
      // Gather local market data
      const location = `${context.businessProfile.coreInfo.primaryLocation.city}, ${context.businessProfile.coreInfo.primaryLocation.country}`;
      const regionalProfile = regionalEngine.getRegionalProfile(location);
      if (regionalProfile) {
        integration.localData = regionalProfile;
        integration.localMarketDataUsed = true;
      }
    } catch (error) {
      console.warn('⚠️ [Revo 1.5] Local market data unavailable:', error);
    }

    return integration;
  }

  /**
   * Generate content structure with integrated data
   */
  private async generateContentStructure(
    context: ContentGenerationContext,
    dataIntegration: any
  ): Promise<Revo15ContentStructure> {
    const profile = context.businessProfile;
    
    // Extract key business differentiators
    const keyDifferentiators = this.extractKeyDifferentiators(profile);
    
    // Generate headline (max 6 words)
    const headline = this.generateHeadline(profile, keyDifferentiators, dataIntegration);
    
    // Generate subheadline (max 25 words)
    const subheadline = this.generateSubheadline(profile, keyDifferentiators, dataIntegration);
    
    // Generate comprehensive caption
    const caption = this.generateCaption(profile, dataIntegration, context);
    
    // Generate call-to-action
    const callToAction = this.generateCallToAction(profile);
    
    // Generate hashtags
    const hashtags = this.generateHashtags(profile, dataIntegration);

    return {
      headline,
      subheadline,
      caption,
      callToAction,
      hashtags
    };
  }

  /**
   * Extract key business differentiators from profile
   */
  private extractKeyDifferentiators(profile: EnhancedBusinessProfile): string[] {
    const differentiators: string[] = [];
    
    // Speed advantages
    if (profile.competitiveDifferentiation?.speedAdvantage?.deliveryTimes?.length) {
      differentiators.push(`Fast delivery: ${profile.competitiveDifferentiation.speedAdvantage.deliveryTimes[0]}`);
    }
    
    // Experience
    if (profile.trustIndicators?.businessLongevity) {
      differentiators.push(`${profile.trustIndicators.businessLongevity} years experience`);
    }
    
    // Quality metrics
    if (profile.competitiveDifferentiation?.qualityMetrics?.certifications?.length) {
      differentiators.push(`Certified: ${profile.competitiveDifferentiation.qualityMetrics.certifications[0]}`);
    }
    
    // Customer base
    if (profile.trustIndicators?.customerBase?.customersServed) {
      differentiators.push(`${profile.trustIndicators.customerBase.customersServed}+ customers served`);
    }
    
    // Review scores
    if (profile.trustIndicators?.reviewScores?.google) {
      differentiators.push(`${profile.trustIndicators.reviewScores.google}/5 Google rating`);
    }

    return differentiators.slice(0, 3); // Top 3 differentiators
  }

  /**
   * Generate headline with strongest differentiator (max 6 words)
   */
  private generateHeadline(
    profile: EnhancedBusinessProfile,
    differentiators: string[],
    dataIntegration: any
  ): string {
    const businessName = profile.coreInfo.businessName;
    
    // Use strongest differentiator or trending topic
    if (differentiators.length > 0) {
      const mainDiff = differentiators[0];
      if (mainDiff.includes('Fast delivery')) {
        return `${businessName}: Lightning Fast Service`;
      } else if (mainDiff.includes('years experience')) {
        return `${businessName}: Trusted Expert Solutions`;
      } else if (mainDiff.includes('Certified')) {
        return `${businessName}: Certified Excellence`;
      }
    }
    
    // Fallback to business type
    return `${businessName}: Premium ${profile.coreInfo.industryCategory}`;
  }

  /**
   * Generate subheadline with specific benefits (max 25 words)
   */
  private generateSubheadline(
    profile: EnhancedBusinessProfile,
    differentiators: string[],
    dataIntegration: any
  ): string {
    const location = profile.coreInfo.primaryLocation.city;
    const mainOffering = profile.offerings[0]?.name || profile.coreInfo.industryCategory;
    
    let subheadline = `Professional ${mainOffering} in ${location}.`;
    
    if (differentiators.length > 0) {
      subheadline += ` ${differentiators[0]}.`;
    }
    
    // Add credibility indicator
    if (profile.trustIndicators?.reviewScores?.google) {
      subheadline += ` ${profile.trustIndicators.reviewScores.google}/5 rated.`;
    }
    
    // Ensure max 25 words
    const words = subheadline.split(' ');
    if (words.length > 25) {
      subheadline = words.slice(0, 25).join(' ') + '...';
    }
    
    return subheadline;
  }

  /**
   * Generate comprehensive caption
   */
  private generateCaption(
    profile: EnhancedBusinessProfile,
    dataIntegration: any,
    context: ContentGenerationContext
  ): string {
    let caption = '';
    
    // Start with business strengths
    caption += `🏢 ${profile.coreInfo.businessName} - Your trusted ${profile.coreInfo.industryCategory.toLowerCase()} partner in ${profile.coreInfo.primaryLocation.city}!\n\n`;
    
    // Add specific services/products
    if (profile.offerings.length > 0) {
      caption += `✨ Our Services:\n`;
      profile.offerings.slice(0, 3).forEach(offering => {
        caption += `• ${offering.name} - ${offering.description.substring(0, 50)}...\n`;
      });
      caption += '\n';
    }
    
    // Add differentiators
    const differentiators = this.extractKeyDifferentiators(profile);
    if (differentiators.length > 0) {
      caption += `🎯 Why Choose Us:\n`;
      differentiators.forEach(diff => {
        caption += `• ${diff}\n`;
      });
      caption += '\n';
    }
    
    // Add contact information
    if (profile.operationalParams.contactChannels) {
      caption += `📞 Get in touch:\n`;
      if (profile.operationalParams.contactChannels.phone) {
        caption += `Phone: ${profile.operationalParams.contactChannels.phone}\n`;
      }
      if (profile.operationalParams.contactChannels.website) {
        caption += `Website: ${profile.operationalParams.contactChannels.website}\n`;
      }
    }
    
    return caption;
  }

  /**
   * Generate call-to-action
   */
  private generateCallToAction(profile: EnhancedBusinessProfile): string {
    const businessHours = profile.operationalParams.operatingHours;
    
    if (profile.operationalParams.contactChannels?.phone) {
      return `Call now: ${profile.operationalParams.contactChannels.phone}`;
    } else if (profile.operationalParams.contactChannels?.website) {
      return `Visit our website for instant quotes`;
    } else {
      return `Contact us today for professional service`;
    }
  }

  /**
   * Generate relevant hashtags
   */
  private generateHashtags(profile: EnhancedBusinessProfile, dataIntegration: any): string[] {
    const hashtags: string[] = [];
    
    // Business-specific hashtags
    hashtags.push(`#${profile.coreInfo.businessName.replace(/\s+/g, '')}`);
    hashtags.push(`#${profile.coreInfo.industryCategory}`);
    hashtags.push(`#${profile.coreInfo.primaryLocation.city.replace(/\s+/g, '')}`);
    
    // Service-specific hashtags
    profile.offerings.slice(0, 2).forEach(offering => {
      hashtags.push(`#${offering.name.replace(/\s+/g, '')}`);
    });
    
    // Add trending hashtags if available
    if (dataIntegration.trendingData?.hashtags) {
      hashtags.push(...dataIntegration.trendingData.hashtags.slice(0, 3));
    }
    
    // Quality/trust hashtags
    if (profile.trustIndicators?.professionalCredentials?.certifications?.length) {
      hashtags.push('#Certified');
    }
    if (profile.trustIndicators?.businessLongevity && profile.trustIndicators.businessLongevity > 5) {
      hashtags.push('#Experienced');
    }
    
    return hashtags.slice(0, 10); // Max 10 hashtags
  }

  /**
   * Apply localization when toggle is enabled
   */
  private async applyLocalization(
    content: Revo15ContentStructure,
    location: { city: string; country: string }
  ): Promise<void> {
    const regionalProfile = regionalEngine.getRegionalProfile(`${location.city}, ${location.country}`);
    
    if (regionalProfile && content.localLanguageElements) {
      // Add 2-3 local language phrases
      const localPhrases = regionalProfile.localSlang?.commonPhrases?.slice(0, 3) || [];
      content.localLanguageElements = localPhrases;
      
      // Integrate local cultural references in caption
      if (regionalProfile.culturalNuances?.length) {
        const culturalRef = regionalProfile.culturalNuances[0].description;
        content.caption += `\n\n🌍 ${culturalRef}`;
      }
    }
  }

  /**
   * Calculate content quality score
   */
  private calculateQualityScore(content: Revo15ContentStructure, profile: EnhancedBusinessProfile): number {
    let score = 0;
    
    // Headline quality (max 20 points)
    if (content.headline && content.headline.split(' ').length <= 6) {
      score += 20;
    }
    
    // Subheadline quality (max 20 points)
    if (content.subheadline && content.subheadline.split(' ').length <= 25) {
      score += 20;
    }
    
    // Caption completeness (max 25 points)
    if (content.caption && content.caption.length > 100) {
      score += 25;
    }
    
    // CTA presence (max 15 points)
    if (content.callToAction) {
      score += 15;
    }
    
    // Hashtag relevance (max 20 points)
    if (content.hashtags && content.hashtags.length >= 5) {
      score += 20;
    }
    
    return Math.min(100, score);
  }
}

// Export singleton instance
export const revo15EnhancedFramework = new Revo15EnhancedContentFramework();
