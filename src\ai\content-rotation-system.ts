/**
 * Content Rotation System - Tracks and rotates content approaches to prevent repetition
 * Implements intelligent approach rotation and content variation tracking
 */

export interface ContentRotationConfig {
  businessName: string;
  businessType: string;
  location: string;
  maxHistorySize?: number;
  rotationInterval?: number;
}

export interface ContentHistory {
  id: string;
  businessName: string;
  businessType: string;
  location: string;
  approach: string;
  headline: string;
  subheadline: string;
  caption: string;
  cta: string;
  timestamp: number;
  performance?: {
    engagement?: number;
    clicks?: number;
    conversions?: number;
  };
}

export interface RotationStrategy {
  approach: string;
  weight: number;
  lastUsed: number;
  usageCount: number;
  successRate: number;
}

export class ContentRotationSystem {
  private contentHistory: ContentHistory[] = [];
  private rotationStrategies: Map<string, RotationStrategy> = new Map();
  private config: ContentRotationConfig;

  constructor(config: ContentRotationConfig) {
    this.config = {
      maxHistorySize: 100,
      rotationInterval: 24 * 60 * 60 * 1000, // 24 hours
      ...config
    };
    this.initializeRotationStrategies();
  }

  /**
   * Initialize rotation strategies with different approaches
   */
  private initializeRotationStrategies(): void {
    const approaches = [
      'DIRECT_BENEFIT', 'SOCIAL_PROOF', 'PROBLEM_SOLUTION', 'LOCAL_INSIDER', 
      'URGENCY_SCARCITY', 'QUESTION_HOOK', 'STATISTIC_LEAD', 'STORY_ANGLE', 
      'COMPARISON', 'NEWS_TREND'
    ];

    approaches.forEach(approach => {
      this.rotationStrategies.set(approach, {
        approach,
        weight: 1.0,
        lastUsed: 0,
        usageCount: 0,
        successRate: 0.5
      });
    });
  }

  /**
   * Get next approach based on rotation strategy
   */
  getNextApproach(): string {
    const now = Date.now();
    const availableStrategies = Array.from(this.rotationStrategies.values())
      .filter(strategy => {
        // Check if enough time has passed since last use
        return (now - strategy.lastUsed) > this.config.rotationInterval!;
      });

    if (availableStrategies.length === 0) {
      // If all strategies were used recently, reset and use least used
      return this.getLeastUsedApproach();
    }

    // Weighted selection based on success rate and usage count
    const weightedStrategies = availableStrategies.map(strategy => ({
      ...strategy,
      adjustedWeight: strategy.weight * (1 - strategy.usageCount * 0.1) * strategy.successRate
    }));

    // Sort by adjusted weight (higher is better)
    weightedStrategies.sort((a, b) => b.adjustedWeight - a.adjustedWeight);

    // Select top 3 and randomly choose from them
    const topStrategies = weightedStrategies.slice(0, 3);
    const selectedStrategy = topStrategies[Math.floor(Math.random() * topStrategies.length)];

    return selectedStrategy.approach;
  }

  /**
   * Get least used approach
   */
  private getLeastUsedApproach(): string {
    const strategies = Array.from(this.rotationStrategies.values());
    strategies.sort((a, b) => a.usageCount - b.usageCount);
    return strategies[0].approach;
  }

  /**
   * Record content generation
   */
  recordContentGeneration(content: Omit<ContentHistory, 'id' | 'timestamp'>): void {
    const contentRecord: ContentHistory = {
      ...content,
      id: this.generateContentId(),
      timestamp: Date.now()
    };

    this.contentHistory.push(contentRecord);

    // Update rotation strategy
    const strategy = this.rotationStrategies.get(content.approach);
    if (strategy) {
      strategy.lastUsed = contentRecord.timestamp;
      strategy.usageCount++;
    }

    // Clean up old history
    this.cleanupHistory();
  }

  /**
   * Update content performance
   */
  updateContentPerformance(contentId: string, performance: ContentHistory['performance']): void {
    const content = this.contentHistory.find(c => c.id === contentId);
    if (content) {
      content.performance = { ...content.performance, ...performance };
      
      // Update strategy success rate
      const strategy = this.rotationStrategies.get(content.approach);
      if (strategy && performance.engagement) {
        strategy.successRate = this.calculateSuccessRate(content.approach);
      }
    }
  }

  /**
   * Calculate success rate for an approach
   */
  private calculateSuccessRate(approach: string): number {
    const approachContent = this.contentHistory.filter(c => c.approach === approach);
    if (approachContent.length === 0) return 0.5;

    const totalEngagement = approachContent.reduce((sum, c) => 
      sum + (c.performance?.engagement || 0), 0);
    const avgEngagement = totalEngagement / approachContent.length;

    // Normalize to 0-1 range (assuming max engagement is 1000)
    return Math.min(avgEngagement / 1000, 1);
  }

  /**
   * Check for repetitive patterns
   */
  checkForRepetition(newContent: Partial<ContentHistory>): {
    isRepetitive: boolean;
    similarContent: ContentHistory[];
    suggestions: string[];
  } {
    const recentContent = this.getRecentContent(7); // Last 7 days
    const similarContent: ContentHistory[] = [];
    const suggestions: string[] = [];

    // Check for similar headlines
    if (newContent.headline) {
      const similarHeadlines = recentContent.filter(c => 
        this.calculateSimilarity(newContent.headline!, c.headline) > 0.7
      );
      similarContent.push(...similarHeadlines);
    }

    // Check for similar subheadlines
    if (newContent.subheadline) {
      const similarSubheadlines = recentContent.filter(c => 
        this.calculateSimilarity(newContent.subheadline!, c.subheadline) > 0.7
      );
      similarContent.push(...similarSubheadlines);
    }

    // Check for similar CTAs
    if (newContent.cta) {
      const similarCTAs = recentContent.filter(c => 
        this.calculateSimilarity(newContent.cta!, c.cta) > 0.7
      );
      similarContent.push(...similarCTAs);
    }

    // Check for overused approaches
    const approachCount = recentContent.filter(c => c.approach === newContent.approach).length;
    if (approachCount > 3) {
      suggestions.push(`Approach "${newContent.approach}" has been used ${approachCount} times recently. Consider using a different approach.`);
    }

    // Generate suggestions for improvement
    if (similarContent.length > 0) {
      suggestions.push('Consider using more unique language and different angles.');
      suggestions.push('Try incorporating more specific business details.');
      suggestions.push('Use different psychological triggers or frameworks.');
    }

    return {
      isRepetitive: similarContent.length > 0 || approachCount > 3,
      similarContent: [...new Set(similarContent)], // Remove duplicates
      suggestions
    };
  }

  /**
   * Calculate text similarity using simple word overlap
   */
  private calculateSimilarity(text1: string, text2: string): number {
    const words1 = text1.toLowerCase().split(/\s+/);
    const words2 = text2.toLowerCase().split(/\s+/);
    
    const intersection = words1.filter(word => words2.includes(word));
    const union = [...new Set([...words1, ...words2])];
    
    return intersection.length / union.length;
  }

  /**
   * Get recent content
   */
  private getRecentContent(days: number): ContentHistory[] {
    const cutoffTime = Date.now() - (days * 24 * 60 * 60 * 1000);
    return this.contentHistory.filter(c => c.timestamp > cutoffTime);
  }

  /**
   * Clean up old history
   */
  private cleanupHistory(): void {
    if (this.contentHistory.length > this.config.maxHistorySize!) {
      // Keep most recent content and remove oldest
      this.contentHistory.sort((a, b) => b.timestamp - a.timestamp);
      this.contentHistory = this.contentHistory.slice(0, this.config.maxHistorySize!);
    }
  }

  /**
   * Generate unique content ID
   */
  private generateContentId(): string {
    return `content_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get rotation statistics
   */
  getRotationStats(): {
    totalContent: number;
    approachUsage: Record<string, number>;
    successRates: Record<string, number>;
    recentApproaches: string[];
  } {
    const approachUsage: Record<string, number> = {};
    const successRates: Record<string, number> = {};
    const recentApproaches = this.getRecentContent(7).map(c => c.approach);

    this.rotationStrategies.forEach((strategy, approach) => {
      approachUsage[approach] = strategy.usageCount;
      successRates[approach] = strategy.successRate;
    });

    return {
      totalContent: this.contentHistory.length,
      approachUsage,
      successRates,
      recentApproaches
    };
  }

  /**
   * Reset rotation strategies (useful for testing or major changes)
   */
  resetRotationStrategies(): void {
    this.rotationStrategies.clear();
    this.initializeRotationStrategies();
  }

  /**
   * Get content variation suggestions
   */
  getVariationSuggestions(approach: string): string[] {
    const suggestions: Record<string, string[]> = {
      'DIRECT_BENEFIT': [
        'Focus on specific, measurable outcomes',
        'Use benefit-driven language: "Get", "Achieve", "Gain"',
        'Make benefits tangible and immediate',
        'Include specific numbers or timeframes'
      ],
      'SOCIAL_PROOF': [
        'Include customer testimonials or success stories',
        'Use statistics and numbers to build credibility',
        'Highlight community validation',
        'Show popularity and trust indicators'
      ],
      'PROBLEM_SOLUTION': [
        'Identify specific pain points your audience faces',
        'Present clear solutions to problems',
        'Use problem-solution language: "Struggling with", "Finally"',
        'Make problems relatable and urgent'
      ],
      'LOCAL_INSIDER': [
        'Use local knowledge and insider tips',
        'Reference local events or trends',
        'Create "insider" feeling with local expertise',
        'Use local language and references'
      ],
      'URGENCY_SCARCITY': [
        'Create time-sensitive or quantity-limited offers',
        'Use urgency language: "Limited time", "Only", "Hurry"',
        'Create FOMO (Fear of Missing Out)',
        'Use scarcity indicators: "Few spots left", "Selling out"'
      ]
    };

    return suggestions[approach] || [
      'Use unexpected word combinations',
      'Employ creative metaphors and analogies',
      'Create memorable, quotable phrases',
      'Use psychological triggers strategically'
    ];
  }
}

// Export singleton instance
export const contentRotationSystem = new ContentRotationSystem({
  businessName: '',
  businessType: '',
  location: ''
});

export default ContentRotationSystem;

