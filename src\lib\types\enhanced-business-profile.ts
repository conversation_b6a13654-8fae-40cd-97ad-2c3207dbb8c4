/**
 * Enhanced Business Profile Interface for Revo 1.5
 * Comprehensive data collection framework for advanced content generation
 */

export interface EnhancedBusinessProfile {
  // Core Company Information
  coreInfo: {
    businessName: string; // Exact legal name
    industryCategory: 'Retail' | 'Restaurant' | 'Services' | 'Healthcare' | 'Tech' | 'Manufacturing' | 'Education' | 'Finance' | 'Real Estate' | 'Other';
    businessModel: 'B2B' | 'B2C' | 'B2B2C' | 'Marketplace';
    primaryLocation: {
      city: string;
      state: string;
      country: string;
    };
    establishmentYear: number;
    businessSize: 'Solo' | 'Small (2-10)' | 'Medium (11-50)' | 'Large (50+)';
  };

  // Products/Services Catalog
  offerings: ProductService[];

  // Competitive Differentiation Matrix
  competitiveDifferentiation: {
    speedAdvantage?: {
      deliveryTimes: string[];
      responseRates: string[];
      processingSpeed: string[];
    };
    qualityMetrics?: {
      materialsUsed: string[];
      experienceYears: number;
      successRates: string[];
      certifications: string[];
    };
    pricingPosition?: {
      percentageSavings: string[];
      valueComparisons: string[];
      packageDeals: string[];
    };
    convenienceFactors?: {
      locationBenefits: string[];
      extendedHours: string[];
      onlineOptions: string[];
    };
    expertiseCredentials?: {
      yearsOfExperience: number;
      specializations: string[];
      teamQualifications: string[];
    };
    serviceExcellence?: {
      responseTimes: string[];
      availability: string[];
      supportChannels: string[];
    };
  };

  // Trust & Authority Indicators
  trustIndicators: {
    businessLongevity: number; // Years in operation
    customerBase?: {
      customersServed: number;
      retentionRates: string[];
    };
    professionalCredentials?: {
      licenses: string[];
      certifications: string[];
      memberships: string[];
    };
    reviewScores?: {
      google?: number; // X.X/5
      yelp?: number; // X.X/5
      industrySpecific?: Array<{ platform: string; score: number }>;
    };
    recognition?: {
      awards: string[];
      mediaMetions: string[];
      industryRankings: string[];
    };
    strategicPartnerships?: {
      keySuppliers: string[];
      distributors: string[];
      collaborators: string[];
    };
    caseStudies?: Array<{
      title: string;
      customerType: string;
      challenge: string;
      solution: string;
      results: string;
      metrics: string[];
    }>;
  };

  // Operational Parameters
  operationalParams: {
    operatingHours: {
      monday?: string;
      tuesday?: string;
      wednesday?: string;
      thursday?: string;
      friday?: string;
      saturday?: string;
      sunday?: string;
      timezone: string;
      holidaySchedule?: string;
    };
    serviceCoverage: {
      geographicBoundaries: string[];
      deliveryZones: string[];
      travelRadius?: string;
    };
    capacityLimits?: {
      maximumCustomers?: number;
      projectVolume?: string;
      bookingAvailability?: string;
    };
    guaranteesWarranties?: {
      specificTerms: string[];
      moneyBackPolicies: string[];
      serviceLevelAgreements: string[];
    };
    contactChannels: {
      phone?: string;
      email?: string;
      website?: string;
      socialMedia?: {
        facebook?: string;
        instagram?: string;
        twitter?: string;
        linkedin?: string;
      };
      physicalAddress?: string;
    };
    emergencyAfterHours?: {
      availability: boolean;
      contactMethod?: string;
      additionalCharges?: string;
    };
  };

  // AI Content Generation Preferences
  contentPreferences: {
    localizationToggle: boolean; // Enable local language integration
    contentTone: 'Professional' | 'Friendly' | 'Casual' | 'Authoritative' | 'Conversational';
    brandVoice: string;
    contentThemes: string[];
    visualStyle: string;
    primaryColor?: string;
    accentColor?: string;
    backgroundColor?: string;
  };

  // System Metadata
  metadata: {
    profileId: string;
    userId: string;
    createdAt: Date;
    updatedAt: Date;
    completionScore: number; // 0-100 based on filled fields
    isActive: boolean;
  };
}

export interface ProductService {
  id: string;
  name: string; // Exact product/service name
  description: string; // Detailed explanation
  pricingStructure: {
    type: 'Fixed' | 'Hourly' | 'Package' | 'Tiered' | 'Custom';
    basePrice?: number;
    currency: string;
    packages?: Array<{
      name: string;
      price: number;
      features: string[];
    }>;
    hourlyRate?: number;
    tiers?: Array<{
      name: string;
      priceRange: string;
      features: string[];
    }>;
  };
  deliveryTimeline: {
    estimatedTime: string; // "24 hours", "same day", "2-3 weeks"
    rushOptions?: Array<{
      timeline: string;
      additionalCost: number;
    }>;
  };
  fulfillmentMethod: 'In-person' | 'Digital delivery' | 'Shipping' | 'Pickup' | 'Hybrid';
  targetCustomerProfile: {
    demographics: string[];
    businessTypes?: string[];
    painPoints: string[];
  };
  primaryBenefits: string[]; // 3-5 specific value propositions
  seasonalVariations?: {
    peakTimes: string[];
    seasonalOfferings: string[];
    holidaySpecials: string[];
  };
  isActive: boolean;
}

// Content Generation Context Interface
export interface ContentGenerationContext {
  businessProfile: EnhancedBusinessProfile;
  platform: 'instagram' | 'facebook' | 'twitter' | 'linkedin' | 'tiktok';
  contentType: 'post' | 'story' | 'reel' | 'carousel';
  rssData?: any; // RSS feed data
  trendingTopics?: any; // Trending topics data
  localMarketData?: any; // Local market insights
  seasonalContext?: {
    season: string;
    holidays: string[];
    localEvents: string[];
  };
}

// Content Quality Standards
export interface ContentQualityStandards {
  specificityRequirement: boolean; // Use concrete numbers, timeframes
  authenticityCheck: boolean; // Only verifiable information
  relevanceFilter: boolean; // RSS/trend data relevance
  completenessValidation: boolean; // Critical profile info check
  toneAdaptation: boolean; // Match industry norms
  problemSolutionFocus: boolean; // Lead with pain points
}

// Content Structure for Revo 1.5
export interface Revo15ContentStructure {
  headline: string; // Max 6 words
  subheadline?: string; // Max 25 words
  caption: string; // Comprehensive but concise
  callToAction?: string; // Action-oriented, specific
  hashtags: string[];
  localLanguageElements?: string[]; // When localization toggle is ON
}
