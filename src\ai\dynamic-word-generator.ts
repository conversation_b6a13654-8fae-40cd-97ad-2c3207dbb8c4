/**
 * Dynamic Word Generator - AI-powered vocabulary generation for fresh, creative content
 * Generates unique word combinations and creative alternatives to prevent repetition
 */

export interface WordGenerationConfig {
  businessType: string;
  location: string;
  creativityLevel: number;
  context?: string;
  avoidWords?: string[];
}

export interface WordPool {
  adjectives: string[];
  verbs: string[];
  nouns: string[];
  phrases: string[];
  emotional: string[];
  action: string[];
  descriptive: string[];
}

export interface CreativeWord {
  word: string;
  category: string;
  intensity: number;
  emotionalTone: 'positive' | 'negative' | 'neutral' | 'excited' | 'calm';
  businessRelevance: number;
  uniqueness: number;
}

export class DynamicWordGenerator {
  private wordCache: Map<string, WordPool> = new Map();
  private usedWords: Set<string> = new Set();
  private maxCacheSize = 50;

  /**
   * Generate dynamic word pool for content creation
   */
  async generateWordPool(config: WordGenerationConfig): Promise<WordPool> {
    const cacheKey = `${config.businessType}_${config.location}_${config.creativityLevel}`;
    
    // Check cache first
    if (this.wordCache.has(cacheKey)) {
      return this.wordCache.get(cacheKey)!;
    }

    const wordPool = await this.createWordPool(config);
    
    // Cache the result
    this.wordCache.set(cacheKey, wordPool);
    
    // Clean up cache if it gets too large
    if (this.wordCache.size > this.maxCacheSize) {
      const firstKey = this.wordCache.keys().next().value;
      this.wordCache.delete(firstKey);
    }

    return wordPool;
  }

  /**
   * Create word pool using AI and business context
   */
  private async createWordPool(config: WordGenerationConfig): Promise<WordPool> {
    const { businessType, location, creativityLevel, context, avoidWords = [] } = config;

    try {
      // Use AI to generate creative words
      const aiWords = await this.generateAIWords(config);
      
      // Combine with business-specific words
      const businessWords = this.getBusinessSpecificWords(businessType);
      
      // Combine with emotional words
      const emotionalWords = this.getEmotionalWords(creativityLevel);
      
      // Filter out avoided words
      const filteredWords = this.filterAvoidedWords({
        ...aiWords,
        ...businessWords,
        ...emotionalWords
      }, avoidWords);

      return filteredWords;
    } catch (error) {
      console.warn('AI word generation failed, using fallback words:', error);
      return this.getFallbackWordPool(businessType, creativityLevel);
    }
  }

  /**
   * Generate words using AI
   */
  private async generateAIWords(config: WordGenerationConfig): Promise<Partial<WordPool>> {
    const { businessType, location, creativityLevel, context } = config;

    const prompt = `Generate creative, unique words for ${businessType} business content in ${location}.

CREATIVITY LEVEL: ${creativityLevel}/100
CONTEXT: ${context || 'General marketing content'}

Generate words that are:
- Fresh and unique (not overused)
- Relevant to ${businessType} business
- Appropriate for ${location} market
- Creative and engaging
- Varied in intensity and tone

Return as JSON with these categories:
{
  "adjectives": ["word1", "word2", ...],
  "verbs": ["word1", "word2", ...],
  "nouns": ["word1", "word2", ...],
  "phrases": ["phrase1", "phrase2", ...],
  "emotional": ["word1", "word2", ...],
  "action": ["word1", "word2", ...],
  "descriptive": ["word1", "word2", ...]
}

Generate 15-20 words per category. Make them creative and unique.`;

    // Simulate AI call (replace with actual AI service)
    const response = await this.callAI(prompt);
    
    try {
      return JSON.parse(response);
    } catch (error) {
      console.warn('Failed to parse AI response, using fallback');
      return this.getFallbackWordPool(businessType, creativityLevel);
    }
  }

  /**
   * Simulate AI call (replace with actual AI service)
   */
  private async callAI(prompt: string): Promise<string> {
    // This would be replaced with actual AI service call
    // For now, return a mock response
    return JSON.stringify({
      adjectives: ['revolutionary', 'cutting-edge', 'innovative', 'breakthrough', 'game-changing'],
      verbs: ['transform', 'revolutionize', 'elevate', 'amplify', 'supercharge'],
      nouns: ['breakthrough', 'innovation', 'excellence', 'mastery', 'precision'],
      phrases: ['next-level', 'world-class', 'industry-leading', 'award-winning', 'premium quality'],
      emotional: ['inspiring', 'exciting', 'amazing', 'incredible', 'outstanding'],
      action: ['achieve', 'conquer', 'dominate', 'excel', 'thrive'],
      descriptive: ['stunning', 'breathtaking', 'mesmerizing', 'captivating', 'compelling']
    });
  }

  /**
   * Get business-specific words
   */
  private getBusinessSpecificWords(businessType: string): Partial<WordPool> {
    const businessWords: Record<string, Partial<WordPool>> = {
      'restaurant': {
        adjectives: ['savory', 'delectable', 'mouthwatering', 'gourmet', 'artisanal'],
        verbs: ['savor', 'indulge', 'feast', 'delight', 'tantalize'],
        nouns: ['cuisine', 'flavor', 'taste', 'delicacy', 'specialty'],
        phrases: ['culinary excellence', 'flavor explosion', 'taste sensation', 'dining experience'],
        emotional: ['delicious', 'satisfying', 'comforting', 'nourishing', 'hearty'],
        action: ['taste', 'enjoy', 'experience', 'discover', 'explore'],
        descriptive: ['fresh', 'authentic', 'premium', 'handcrafted', 'traditional']
      },
      'bakery': {
        adjectives: ['fresh', 'flaky', 'buttery', 'sweet', 'golden'],
        verbs: ['bake', 'craft', 'create', 'decorate', 'frost'],
        nouns: ['pastry', 'bread', 'cake', 'cookie', 'treat'],
        phrases: ['oven-fresh', 'hand-baked', 'artisan quality', 'homemade goodness'],
        emotional: ['comforting', 'nostalgic', 'sweet', 'cozy', 'warm'],
        action: ['bake', 'decorate', 'serve', 'share', 'celebrate'],
        descriptive: ['fluffy', 'crispy', 'moist', 'tender', 'delicate']
      },
      'fitness': {
        adjectives: ['powerful', 'energetic', 'dynamic', 'intense', 'motivating'],
        verbs: ['transform', 'achieve', 'conquer', 'dominate', 'excel'],
        nouns: ['strength', 'endurance', 'power', 'fitness', 'wellness'],
        phrases: ['peak performance', 'fitness journey', 'strength training', 'wellness lifestyle'],
        emotional: ['empowering', 'energizing', 'motivating', 'inspiring', 'confident'],
        action: ['train', 'push', 'achieve', 'excel', 'succeed'],
        descriptive: ['strong', 'fit', 'toned', 'athletic', 'powerful']
      },
      'beauty': {
        adjectives: ['radiant', 'flawless', 'stunning', 'gorgeous', 'elegant'],
        verbs: ['enhance', 'highlight', 'accentuate', 'transform', 'glow'],
        nouns: ['beauty', 'glow', 'radiance', 'elegance', 'style'],
        phrases: ['beauty transformation', 'glowing skin', 'elegant style', 'radiant beauty'],
        emotional: ['beautiful', 'confident', 'glamorous', 'elegant', 'stunning'],
        action: ['enhance', 'highlight', 'accentuate', 'transform', 'glow'],
        descriptive: ['smooth', 'silky', 'luminous', 'flawless', 'radiant']
      },
      'retail': {
        adjectives: ['exclusive', 'premium', 'unique', 'trendy', 'stylish'],
        verbs: ['discover', 'explore', 'find', 'uncover', 'reveal'],
        nouns: ['collection', 'selection', 'trend', 'style', 'fashion'],
        phrases: ['exclusive collection', 'trendy selection', 'style statement', 'fashion forward'],
        emotional: ['exciting', 'trendy', 'stylish', 'exclusive', 'desirable'],
        action: ['discover', 'explore', 'find', 'shop', 'browse'],
        descriptive: ['trendy', 'stylish', 'exclusive', 'unique', 'premium']
      },
      'tech': {
        adjectives: ['innovative', 'cutting-edge', 'advanced', 'smart', 'intelligent'],
        verbs: ['innovate', 'advance', 'evolve', 'upgrade', 'enhance'],
        nouns: ['innovation', 'technology', 'solution', 'system', 'platform'],
        phrases: ['next-generation', 'cutting-edge tech', 'smart solutions', 'digital innovation'],
        emotional: ['exciting', 'innovative', 'futuristic', 'advanced', 'intelligent'],
        action: ['innovate', 'advance', 'evolve', 'upgrade', 'streamline'],
        descriptive: ['smart', 'intelligent', 'advanced', 'cutting-edge', 'innovative']
      },
      'service': {
        adjectives: ['professional', 'expert', 'skilled', 'experienced', 'reliable'],
        verbs: ['serve', 'deliver', 'provide', 'support', 'assist'],
        nouns: ['service', 'expertise', 'solution', 'support', 'assistance'],
        phrases: ['professional service', 'expert solutions', 'reliable support', 'quality assistance'],
        emotional: ['trustworthy', 'reliable', 'professional', 'confident', 'assured'],
        action: ['serve', 'deliver', 'provide', 'support', 'assist'],
        descriptive: ['professional', 'expert', 'skilled', 'experienced', 'reliable']
      }
    };

    return businessWords[businessType.toLowerCase()] || businessWords['service'];
  }

  /**
   * Get emotional words based on creativity level
   */
  private getEmotionalWords(creativityLevel: number): Partial<WordPool> {
    const baseWords = {
      adjectives: ['amazing', 'incredible', 'outstanding', 'remarkable', 'extraordinary'],
      verbs: ['inspire', 'motivate', 'energize', 'empower', 'thrill'],
      nouns: ['excitement', 'passion', 'enthusiasm', 'energy', 'vitality'],
      phrases: ['amazing experience', 'incredible results', 'outstanding quality', 'remarkable service'],
      emotional: ['excited', 'thrilled', 'amazed', 'inspired', 'motivated'],
      action: ['inspire', 'motivate', 'energize', 'empower', 'thrill'],
      descriptive: ['exciting', 'thrilling', 'amazing', 'inspiring', 'motivating']
    };

    if (creativityLevel > 70) {
      return {
        adjectives: [...baseWords.adjectives, 'mind-blowing', 'phenomenal', 'spectacular', 'breathtaking', 'mesmerizing'],
        verbs: [...baseWords.verbs, 'astound', 'mesmerize', 'captivate', 'enchant', 'bewitch'],
        nouns: [...baseWords.nouns, 'wonder', 'magic', 'brilliance', 'genius', 'mastery'],
        phrases: [...baseWords.phrases, 'mind-blowing experience', 'phenomenal results', 'spectacular quality'],
        emotional: [...baseWords.emotional, 'astounded', 'mesmerized', 'captivated', 'enchanted', 'bewitched'],
        action: [...baseWords.action, 'astound', 'mesmerize', 'captivate', 'enchant', 'bewitch'],
        descriptive: [...baseWords.descriptive, 'mind-blowing', 'phenomenal', 'spectacular', 'breathtaking', 'mesmerizing']
      };
    }

    return baseWords;
  }

  /**
   * Filter out avoided words
   */
  private filterAvoidedWords(wordPool: Partial<WordPool>, avoidWords: string[]): WordPool {
    const filterWords = (words: string[]) => 
      words.filter(word => !avoidWords.some(avoid => 
        word.toLowerCase().includes(avoid.toLowerCase())
      ));

    return {
      adjectives: filterWords(wordPool.adjectives || []),
      verbs: filterWords(wordPool.verbs || []),
      nouns: filterWords(wordPool.nouns || []),
      phrases: filterWords(wordPool.phrases || []),
      emotional: filterWords(wordPool.emotional || []),
      action: filterWords(wordPool.action || []),
      descriptive: filterWords(wordPool.descriptive || [])
    };
  }

  /**
   * Get fallback word pool when AI fails
   */
  private getFallbackWordPool(businessType: string, creativityLevel: number): WordPool {
    const businessWords = this.getBusinessSpecificWords(businessType);
    const emotionalWords = this.getEmotionalWords(creativityLevel);

    return {
      adjectives: [...(businessWords.adjectives || []), ...(emotionalWords.adjectives || [])],
      verbs: [...(businessWords.verbs || []), ...(emotionalWords.verbs || [])],
      nouns: [...(businessWords.nouns || []), ...(emotionalWords.nouns || [])],
      phrases: [...(businessWords.phrases || []), ...(emotionalWords.phrases || [])],
      emotional: [...(businessWords.emotional || []), ...(emotionalWords.emotional || [])],
      action: [...(businessWords.action || []), ...(emotionalWords.action || [])],
      descriptive: [...(businessWords.descriptive || []), ...(emotionalWords.descriptive || [])]
    };
  }

  /**
   * Generate creative word combinations
   */
  generateWordCombinations(wordPool: WordPool, count: number = 10): string[] {
    const combinations: string[] = [];
    const categories = Object.keys(wordPool) as (keyof WordPool)[];

    for (let i = 0; i < count; i++) {
      const category1 = categories[Math.floor(Math.random() * categories.length)];
      const category2 = categories[Math.floor(Math.random() * categories.length)];
      
      const word1 = wordPool[category1][Math.floor(Math.random() * wordPool[category1].length)];
      const word2 = wordPool[category2][Math.floor(Math.random() * wordPool[category2].length)];
      
      combinations.push(`${word1} ${word2}`);
    }

    return combinations;
  }

  /**
   * Get creative alternatives for overused words
   */
  getCreativeAlternatives(overusedWord: string, wordPool: WordPool): string[] {
    const alternatives: string[] = [];
    
    // Find words with similar meaning but different intensity
    const allWords = [
      ...wordPool.adjectives,
      ...wordPool.verbs,
      ...wordPool.nouns,
      ...wordPool.emotional,
      ...wordPool.descriptive
    ];

    // Simple similarity check (in a real implementation, you'd use more sophisticated NLP)
    const similarWords = allWords.filter(word => 
      word.length > 3 && 
      !word.toLowerCase().includes(overusedWord.toLowerCase()) &&
      Math.random() > 0.7 // Random selection for now
    );

    return similarWords.slice(0, 5);
  }

  /**
   * Track used words to avoid repetition
   */
  trackUsedWord(word: string): void {
    this.usedWords.add(word.toLowerCase());
  }

  /**
   * Check if word has been overused
   */
  isWordOverused(word: string): boolean {
    return this.usedWords.has(word.toLowerCase());
  }

  /**
   * Get fresh words that haven't been used recently
   */
  getFreshWords(wordPool: WordPool, count: number = 5): string[] {
    const allWords = [
      ...wordPool.adjectives,
      ...wordPool.verbs,
      ...wordPool.nouns,
      ...wordPool.emotional,
      ...wordPool.descriptive
    ];

    const freshWords = allWords.filter(word => !this.isWordOverused(word));
    
    // Shuffle and return requested count
    return freshWords.sort(() => Math.random() - 0.5).slice(0, count);
  }

  /**
   * Clear used words cache
   */
  clearUsedWords(): void {
    this.usedWords.clear();
  }

  /**
   * Get word pool statistics
   */
  getWordPoolStats(wordPool: WordPool): {
    totalWords: number;
    categoryCounts: Record<string, number>;
    usedWordsCount: number;
  } {
    const categoryCounts: Record<string, number> = {};
    let totalWords = 0;

    Object.entries(wordPool).forEach(([category, words]) => {
      categoryCounts[category] = words.length;
      totalWords += words.length;
    });

    return {
      totalWords,
      categoryCounts,
      usedWordsCount: this.usedWords.size
    };
  }
}

// Export singleton instance
export const dynamicWordGenerator = new DynamicWordGenerator();

export default DynamicWordGenerator;

