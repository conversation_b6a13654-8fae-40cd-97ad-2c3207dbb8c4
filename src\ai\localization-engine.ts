/**
 * Localization Engine for Revo 1.5
 * Integrates local language phrases and cultural references based on business location
 */

export interface LocalizationProfile {
  country: string;
  region: string;
  primaryLanguages: string[];
  commonPhrases: LocalPhrase[];
  culturalReferences: CulturalReference[];
  businessEtiquette: BusinessEtiquette;
  seasonalFactors: SeasonalFactor[];
  localEvents: LocalEvent[];
}

export interface LocalPhrase {
  phrase: string;
  translation: string;
  context: 'greeting' | 'appreciation' | 'urgency' | 'quality' | 'service' | 'general';
  formality: 'formal' | 'casual' | 'friendly';
}

export interface CulturalReference {
  reference: string;
  explanation: string;
  appropriateContext: string[];
  businessRelevance: 'high' | 'medium' | 'low';
}

export interface BusinessEtiquette {
  communicationStyle: 'direct' | 'indirect' | 'formal' | 'casual';
  preferredTone: string[];
  avoidTerms: string[];
  respectfulApproaches: string[];
}

export interface SeasonalFactor {
  season: string;
  culturalSignificance: string;
  businessImplications: string[];
  marketingOpportunities: string[];
}

export interface LocalEvent {
  eventName: string;
  timeframe: string;
  businessRelevance: string;
  marketingAngle: string;
}

export class LocalizationEngine {
  private localizationProfiles: Map<string, LocalizationProfile> = new Map();

  constructor() {
    this.initializeLocalizationProfiles();
  }

  /**
   * Initialize localization profiles for different regions
   */
  private initializeLocalizationProfiles(): void {
    // Kenya Profile
    this.localizationProfiles.set('kenya', {
      country: 'Kenya',
      region: 'East Africa',
      primaryLanguages: ['English', 'Swahili'],
      commonPhrases: [
        { phrase: 'Karibu sana', translation: 'You are very welcome', context: 'greeting', formality: 'friendly' },
        { phrase: 'Asante sana', translation: 'Thank you very much', context: 'appreciation', formality: 'friendly' },
        { phrase: 'Haraka haraka', translation: 'Quickly quickly', context: 'urgency', formality: 'casual' },
        { phrase: 'Bora kabisa', translation: 'The very best', context: 'quality', formality: 'casual' },
        { phrase: 'Huduma bora', translation: 'Excellent service', context: 'service', formality: 'formal' }
      ],
      culturalReferences: [
        {
          reference: 'Ubuntu philosophy',
          explanation: 'Community-centered approach to business',
          appropriateContext: ['teamwork', 'customer service', 'community involvement'],
          businessRelevance: 'high'
        },
        {
          reference: 'Harambee spirit',
          explanation: 'Pulling together for common goals',
          appropriateContext: ['collaboration', 'community projects', 'group achievements'],
          businessRelevance: 'high'
        }
      ],
      businessEtiquette: {
        communicationStyle: 'friendly',
        preferredTone: ['respectful', 'warm', 'community-focused'],
        avoidTerms: ['aggressive', 'pushy', 'individualistic'],
        respectfulApproaches: ['acknowledge community', 'show respect for elders', 'emphasize relationships']
      },
      seasonalFactors: [
        {
          season: 'Long Rains (March-May)',
          culturalSignificance: 'Planting season, renewal, growth',
          businessImplications: ['indoor services popular', 'delivery challenges', 'agricultural focus'],
          marketingOpportunities: ['growth themes', 'renewal messaging', 'preparation services']
        }
      ],
      localEvents: [
        {
          eventName: 'Mashujaa Day',
          timeframe: 'October 20',
          businessRelevance: 'National pride and heroism',
          marketingAngle: 'Celebrate local heroes and achievements'
        }
      ]
    });

    // Nigeria Profile
    this.localizationProfiles.set('nigeria', {
      country: 'Nigeria',
      region: 'West Africa',
      primaryLanguages: ['English', 'Hausa', 'Yoruba', 'Igbo'],
      commonPhrases: [
        { phrase: 'How far?', translation: 'How are you?', context: 'greeting', formality: 'casual' },
        { phrase: 'No wahala', translation: 'No problem', context: 'service', formality: 'casual' },
        { phrase: 'Sharp sharp', translation: 'Very quickly', context: 'urgency', formality: 'casual' },
        { phrase: 'Correct!', translation: 'Exactly right!', context: 'quality', formality: 'casual' },
        { phrase: 'I hail you', translation: 'I respect/appreciate you', context: 'appreciation', formality: 'friendly' }
      ],
      culturalReferences: [
        {
          reference: 'Naija spirit',
          explanation: 'Resilient, entrepreneurial Nigerian attitude',
          appropriateContext: ['overcoming challenges', 'innovation', 'determination'],
          businessRelevance: 'high'
        }
      ],
      businessEtiquette: {
        communicationStyle: 'direct',
        preferredTone: ['energetic', 'confident', 'solution-focused'],
        avoidTerms: ['slow', 'complicated', 'bureaucratic'],
        respectfulApproaches: ['show energy', 'emphasize results', 'acknowledge hustle culture']
      },
      seasonalFactors: [
        {
          season: 'Harmattan (December-February)',
          culturalSignificance: 'Dry season, clear skies, visibility',
          businessImplications: ['outdoor events popular', 'construction season', 'travel season'],
          marketingOpportunities: ['clarity themes', 'visibility messaging', 'outdoor services']
        }
      ],
      localEvents: [
        {
          eventName: 'Independence Day',
          timeframe: 'October 1',
          businessRelevance: 'National pride and progress',
          marketingAngle: 'Celebrate Nigerian achievements and progress'
        }
      ]
    });

    // South Africa Profile
    this.localizationProfiles.set('south_africa', {
      country: 'South Africa',
      region: 'Southern Africa',
      primaryLanguages: ['English', 'Afrikaans', 'Zulu', 'Xhosa'],
      commonPhrases: [
        { phrase: 'Howzit', translation: 'How are you?', context: 'greeting', formality: 'casual' },
        { phrase: 'Sharp', translation: 'Good/Okay', context: 'general', formality: 'casual' },
        { phrase: 'Lekker', translation: 'Nice/Good', context: 'quality', formality: 'casual' },
        { phrase: 'Eish', translation: 'Oh no/Wow', context: 'general', formality: 'casual' },
        { phrase: 'Dankie', translation: 'Thank you', context: 'appreciation', formality: 'friendly' }
      ],
      culturalReferences: [
        {
          reference: 'Rainbow Nation',
          explanation: 'Diversity and unity in South Africa',
          appropriateContext: ['diversity', 'inclusion', 'unity'],
          businessRelevance: 'high'
        }
      ],
      businessEtiquette: {
        communicationStyle: 'casual',
        preferredTone: ['friendly', 'inclusive', 'straightforward'],
        avoidTerms: ['exclusive', 'elitist', 'complicated'],
        respectfulApproaches: ['acknowledge diversity', 'be inclusive', 'keep it real']
      },
      seasonalFactors: [
        {
          season: 'Summer (December-February)',
          culturalSignificance: 'Holiday season, outdoor lifestyle',
          businessImplications: ['tourism peak', 'outdoor services', 'holiday spending'],
          marketingOpportunities: ['summer themes', 'holiday messaging', 'outdoor activities']
        }
      ],
      localEvents: [
        {
          eventName: 'Heritage Day',
          timeframe: 'September 24',
          businessRelevance: 'Cultural diversity celebration',
          marketingAngle: 'Celebrate cultural heritage and diversity'
        }
      ]
    });

    // Add more profiles as needed...
  }

  /**
   * Get localization profile for a specific location
   */
  getLocalizationProfile(location: string): LocalizationProfile | null {
    const locationLower = location.toLowerCase();

    // Kenya detection
    if (locationLower.includes('kenya') || locationLower.includes('nairobi') ||
        locationLower.includes('mombasa') || locationLower.includes('kisumu')) {
      return this.localizationProfiles.get('kenya') || null;
    }

    // Nigeria detection
    if (locationLower.includes('nigeria') || locationLower.includes('lagos') ||
        locationLower.includes('abuja') || locationLower.includes('kano')) {
      return this.localizationProfiles.get('nigeria') || null;
    }

    // South Africa detection
    if (locationLower.includes('south africa') || locationLower.includes('johannesburg') ||
        locationLower.includes('cape town') || locationLower.includes('durban')) {
      return this.localizationProfiles.get('south_africa') || null;
    }

    return null;
  }

  /**
   * Apply localization to content
   */
  applyLocalization(
    content: string,
    location: string,
    context: 'greeting' | 'appreciation' | 'urgency' | 'quality' | 'service' | 'general' = 'general'
  ): {
    localizedContent: string;
    localPhrases: string[];
    culturalReferences: string[];
  } {
    const profile = this.getLocalizationProfile(location);
    
    if (!profile) {
      return {
        localizedContent: content,
        localPhrases: [],
        culturalReferences: []
      };
    }

    let localizedContent = content;
    const localPhrases: string[] = [];
    const culturalReferences: string[] = [];

    // Add 2-3 relevant local phrases
    const relevantPhrases = profile.commonPhrases
      .filter(phrase => phrase.context === context || phrase.context === 'general')
      .slice(0, 3);

    relevantPhrases.forEach(phrase => {
      localPhrases.push(`${phrase.phrase} (${phrase.translation})`);
    });

    // Add cultural references if highly relevant
    const relevantCulturalRefs = profile.culturalReferences
      .filter(ref => ref.businessRelevance === 'high')
      .slice(0, 1);

    relevantCulturalRefs.forEach(ref => {
      culturalReferences.push(ref.explanation);
      localizedContent += `\n\n🌍 Embracing the ${ref.reference} - ${ref.explanation}`;
    });

    // Add local phrases to content
    if (localPhrases.length > 0) {
      localizedContent += `\n\n🗣️ ${localPhrases.join(' • ')}`;
    }

    return {
      localizedContent,
      localPhrases,
      culturalReferences
    };
  }

  /**
   * Get seasonal marketing opportunities
   */
  getSeasonalOpportunities(location: string): SeasonalFactor[] {
    const profile = this.getLocalizationProfile(location);
    return profile?.seasonalFactors || [];
  }

  /**
   * Get local events for marketing
   */
  getLocalEvents(location: string): LocalEvent[] {
    const profile = this.getLocalizationProfile(location);
    return profile?.localEvents || [];
  }

  /**
   * Get business etiquette guidelines
   */
  getBusinessEtiquette(location: string): BusinessEtiquette | null {
    const profile = this.getLocalizationProfile(location);
    return profile?.businessEtiquette || null;
  }

  /**
   * Validate content against local business etiquette
   */
  validateContentEtiquette(content: string, location: string): {
    isAppropriate: boolean;
    suggestions: string[];
    warnings: string[];
  } {
    const etiquette = this.getBusinessEtiquette(location);
    
    if (!etiquette) {
      return {
        isAppropriate: true,
        suggestions: [],
        warnings: []
      };
    }

    const suggestions: string[] = [];
    const warnings: string[] = [];
    const contentLower = content.toLowerCase();

    // Check for terms to avoid
    etiquette.avoidTerms.forEach(term => {
      if (contentLower.includes(term.toLowerCase())) {
        warnings.push(`Consider avoiding the term "${term}" - it may not resonate well locally`);
      }
    });

    // Suggest preferred tones
    const hasPreferredTone = etiquette.preferredTone.some(tone => 
      contentLower.includes(tone.toLowerCase())
    );

    if (!hasPreferredTone) {
      suggestions.push(`Consider incorporating a ${etiquette.preferredTone.join(' or ')} tone`);
    }

    return {
      isAppropriate: warnings.length === 0,
      suggestions,
      warnings
    };
  }
}

// Export singleton instance
export const localizationEngine = new LocalizationEngine();
