/**
 * ML Content Optimizer - Machine learning integration for content optimization
 * Learns from high-performing content to improve future generation
 */

export interface MLConfig {
  businessName: string;
  businessType: string;
  location: string;
  platform: string;
  learningRate: number;
  modelVersion: string;
}

export interface ContentPerformance {
  contentId: string;
  headline: string;
  subheadline: string;
  caption: string;
  cta: string;
  framework: string;
  metrics: {
    impressions: number;
    clicks: number;
    conversions: number;
    engagement: number;
    shares: number;
    comments: number;
  };
  timestamp: number;
  businessContext: {
    businessType: string;
    location: string;
    platform: string;
  };
}

export interface MLInsights {
  topPerformingPatterns: Array<{
    pattern: string;
    performance: number;
    frequency: number;
    confidence: number;
  }>;
  underperformingPatterns: Array<{
    pattern: string;
    performance: number;
    frequency: number;
    confidence: number;
  }>;
  recommendations: Array<{
    type: 'pattern' | 'framework' | 'style' | 'timing';
    recommendation: string;
    expectedImprovement: number;
    confidence: number;
  }>;
  modelAccuracy: number;
  learningProgress: number;
}

export interface MLPrediction {
  contentId: string;
  predictedPerformance: {
    impressions: number;
    clicks: number;
    conversions: number;
    engagement: number;
  };
  confidence: number;
  reasoning: string[];
  riskFactors: string[];
}

export class MLContentOptimizer {
  private performanceData: ContentPerformance[] = [];
  private patternWeights: Map<string, number> = new Map();
  private frameworkPerformance: Map<string, number> = new Map();
  private businessTypeInsights: Map<string, any> = new Map();
  private locationInsights: Map<string, any> = new Map();
  private platformInsights: Map<string, any> = new Map();
  private modelVersion: string = '1.0.0';
  private learningRate: number = 0.1;

  constructor(config: MLConfig) {
    this.modelVersion = config.modelVersion;
    this.learningRate = config.learningRate;
    this.initializeModel();
  }

  /**
   * Initialize the ML model
   */
  private initializeModel(): void {
    // Initialize pattern weights with default values
    const defaultPatterns = [
      'emotional_trigger', 'local_reference', 'urgency_creator', 'benefit_focused',
      'question_hook', 'statistic_lead', 'storytelling', 'social_proof',
      'authority_positioning', 'curiosity_hook', 'transformation', 'exclusivity'
    ];

    defaultPatterns.forEach(pattern => {
      this.patternWeights.set(pattern, 0.5);
    });

    // Initialize framework performance tracking
    const frameworks = [
      'AIDA', 'PAS', 'BEFORE_AFTER_BRIDGE', 'STORYTELLING', 'SOCIAL_PROOF',
      'PROBLEM_SOLUTION', 'BENEFIT_FOCUSED', 'URGENCY_SCARCITY', 'CURIOSITY_HOOK',
      'AUTHORITY_POSITIONING', 'EMOTIONAL_CONNECTION', 'LOCAL_INSIDER'
    ];

    frameworks.forEach(framework => {
      this.frameworkPerformance.set(framework, 0.5);
    });
  }

  /**
   * Record content performance for learning
   */
  recordPerformance(performance: ContentPerformance): void {
    this.performanceData.push(performance);
    
    // Update pattern weights based on performance
    this.updatePatternWeights(performance);
    
    // Update framework performance
    this.updateFrameworkPerformance(performance);
    
    // Update business type insights
    this.updateBusinessTypeInsights(performance);
    
    // Update location insights
    this.updateLocationInsights(performance);
    
    // Update platform insights
    this.updatePlatformInsights(performance);
    
    // Keep only recent data (last 1000 entries)
    if (this.performanceData.length > 1000) {
      this.performanceData = this.performanceData.slice(-1000);
    }
  }

  /**
   * Update pattern weights based on performance
   */
  private updatePatternWeights(performance: ContentPerformance): void {
    const patterns = this.extractPatterns(performance);
    const performanceScore = this.calculatePerformanceScore(performance.metrics);
    
    patterns.forEach(pattern => {
      const currentWeight = this.patternWeights.get(pattern) || 0.5;
      const newWeight = currentWeight + this.learningRate * (performanceScore - 0.5);
      this.patternWeights.set(pattern, Math.max(0, Math.min(1, newWeight)));
    });
  }

  /**
   * Update framework performance
   */
  private updateFrameworkPerformance(performance: ContentPerformance): void {
    const framework = performance.framework;
    const performanceScore = this.calculatePerformanceScore(performance.metrics);
    
    const currentPerformance = this.frameworkPerformance.get(framework) || 0.5;
    const newPerformance = currentPerformance + this.learningRate * (performanceScore - 0.5);
    this.frameworkPerformance.set(framework, Math.max(0, Math.min(1, newPerformance)));
  }

  /**
   * Update business type insights
   */
  private updateBusinessTypeInsights(performance: ContentPerformance): void {
    const businessType = performance.businessContext.businessType;
    const performanceScore = this.calculatePerformanceScore(performance.metrics);
    
    if (!this.businessTypeInsights.has(businessType)) {
      this.businessTypeInsights.set(businessType, {
        totalPerformance: 0,
        count: 0,
        avgPerformance: 0,
        topPatterns: new Map(),
        topFrameworks: new Map()
      });
    }
    
    const insights = this.businessTypeInsights.get(businessType)!;
    insights.totalPerformance += performanceScore;
    insights.count += 1;
    insights.avgPerformance = insights.totalPerformance / insights.count;
    
    // Update top patterns for this business type
    const patterns = this.extractPatterns(performance);
    patterns.forEach(pattern => {
      const currentCount = insights.topPatterns.get(pattern) || 0;
      insights.topPatterns.set(pattern, currentCount + 1);
    });
    
    // Update top frameworks for this business type
    const framework = performance.framework;
    const currentCount = insights.topFrameworks.get(framework) || 0;
    insights.topFrameworks.set(framework, currentCount + 1);
  }

  /**
   * Update location insights
   */
  private updateLocationInsights(performance: ContentPerformance): void {
    const location = performance.businessContext.location;
    const performanceScore = this.calculatePerformanceScore(performance.metrics);
    
    if (!this.locationInsights.has(location)) {
      this.locationInsights.set(location, {
        totalPerformance: 0,
        count: 0,
        avgPerformance: 0,
        topPatterns: new Map(),
        topFrameworks: new Map()
      });
    }
    
    const insights = this.locationInsights.get(location)!;
    insights.totalPerformance += performanceScore;
    insights.count += 1;
    insights.avgPerformance = insights.totalPerformance / insights.count;
    
    // Update top patterns for this location
    const patterns = this.extractPatterns(performance);
    patterns.forEach(pattern => {
      const currentCount = insights.topPatterns.get(pattern) || 0;
      insights.topPatterns.set(pattern, currentCount + 1);
    });
    
    // Update top frameworks for this location
    const framework = performance.framework;
    const currentCount = insights.topFrameworks.get(framework) || 0;
    insights.topFrameworks.set(framework, currentCount + 1);
  }

  /**
   * Update platform insights
   */
  private updatePlatformInsights(performance: ContentPerformance): void {
    const platform = performance.businessContext.platform;
    const performanceScore = this.calculatePerformanceScore(performance.metrics);
    
    if (!this.platformInsights.has(platform)) {
      this.platformInsights.set(platform, {
        totalPerformance: 0,
        count: 0,
        avgPerformance: 0,
        topPatterns: new Map(),
        topFrameworks: new Map()
      });
    }
    
    const insights = this.platformInsights.get(platform)!;
    insights.totalPerformance += performanceScore;
    insights.count += 1;
    insights.avgPerformance = insights.totalPerformance / insights.count;
    
    // Update top patterns for this platform
    const patterns = this.extractPatterns(performance);
    patterns.forEach(pattern => {
      const currentCount = insights.topPatterns.get(pattern) || 0;
      insights.topPatterns.set(pattern, currentCount + 1);
    });
    
    // Update top frameworks for this platform
    const framework = performance.framework;
    const currentCount = insights.topFrameworks.get(framework) || 0;
    insights.topFrameworks.set(framework, currentCount + 1);
  }

  /**
   * Extract patterns from content
   */
  private extractPatterns(performance: ContentPerformance): string[] {
    const patterns: string[] = [];
    const content = `${performance.headline} ${performance.subheadline} ${performance.caption} ${performance.cta}`.toLowerCase();
    
    // Emotional triggers
    if (content.includes('amazing') || content.includes('incredible') || content.includes('outstanding')) {
      patterns.push('emotional_trigger');
    }
    
    // Local references
    if (content.includes(performance.businessContext.location.toLowerCase())) {
      patterns.push('local_reference');
    }
    
    // Urgency creators
    if (content.includes('now') || content.includes('today') || content.includes('limited') || content.includes('hurry')) {
      patterns.push('urgency_creator');
    }
    
    // Benefit focused
    if (content.includes('benefit') || content.includes('advantage') || content.includes('gain') || content.includes('get')) {
      patterns.push('benefit_focused');
    }
    
    // Question hooks
    if (content.includes('?') || content.includes('how') || content.includes('what') || content.includes('why')) {
      patterns.push('question_hook');
    }
    
    // Statistic leads
    if (content.includes('%') || content.includes('million') || content.includes('thousand') || content.includes('billion')) {
      patterns.push('statistic_lead');
    }
    
    // Storytelling
    if (content.includes('story') || content.includes('journey') || content.includes('experience') || content.includes('transformation')) {
      patterns.push('storytelling');
    }
    
    // Social proof
    if (content.includes('customers') || content.includes('clients') || content.includes('testimonials') || content.includes('reviews')) {
      patterns.push('social_proof');
    }
    
    // Authority positioning
    if (content.includes('expert') || content.includes('professional') || content.includes('specialist') || content.includes('leader')) {
      patterns.push('authority_positioning');
    }
    
    // Curiosity hooks
    if (content.includes('discover') || content.includes('secret') || content.includes('reveal') || content.includes('uncover')) {
      patterns.push('curiosity_hook');
    }
    
    // Transformation
    if (content.includes('transform') || content.includes('change') || content.includes('improve') || content.includes('enhance')) {
      patterns.push('transformation');
    }
    
    // Exclusivity
    if (content.includes('exclusive') || content.includes('limited') || content.includes('special') || content.includes('unique')) {
      patterns.push('exclusivity');
    }
    
    return patterns;
  }

  /**
   * Calculate performance score
   */
  private calculatePerformanceScore(metrics: ContentPerformance['metrics']): number {
    const { impressions, clicks, conversions, engagement, shares, comments } = metrics;
    
    // Normalize metrics (assuming max values)
    const normalizedImpressions = Math.min(impressions / 10000, 1);
    const normalizedClicks = Math.min(clicks / 1000, 1);
    const normalizedConversions = Math.min(conversions / 100, 1);
    const normalizedEngagement = Math.min(engagement / 1000, 1);
    const normalizedShares = Math.min(shares / 100, 1);
    const normalizedComments = Math.min(comments / 50, 1);
    
    // Weighted average
    const score = (
      normalizedImpressions * 0.2 +
      normalizedClicks * 0.25 +
      normalizedConversions * 0.3 +
      normalizedEngagement * 0.15 +
      normalizedShares * 0.05 +
      normalizedComments * 0.05
    );
    
    return Math.max(0, Math.min(1, score));
  }

  /**
   * Get ML insights
   */
  getMLInsights(): MLInsights {
    const topPerformingPatterns = this.getTopPerformingPatterns();
    const underperformingPatterns = this.getUnderperformingPatterns();
    const recommendations = this.generateRecommendations();
    const modelAccuracy = this.calculateModelAccuracy();
    const learningProgress = this.calculateLearningProgress();
    
    return {
      topPerformingPatterns,
      underperformingPatterns,
      recommendations,
      modelAccuracy,
      learningProgress
    };
  }

  /**
   * Get top performing patterns
   */
  private getTopPerformingPatterns(): MLInsights['topPerformingPatterns'] {
    const patternPerformance: Array<{ pattern: string; performance: number; frequency: number }> = [];
    
    this.patternWeights.forEach((weight, pattern) => {
      const frequency = this.performanceData.filter(p => 
        this.extractPatterns(p).includes(pattern)
      ).length;
      
      patternPerformance.push({
        pattern,
        performance: weight,
        frequency
      });
    });
    
    return patternPerformance
      .sort((a, b) => b.performance - a.performance)
      .slice(0, 5)
      .map(p => ({
        ...p,
        confidence: Math.min(p.performance * 2, 1)
      }));
  }

  /**
   * Get underperforming patterns
   */
  private getUnderperformingPatterns(): MLInsights['underperformingPatterns'] {
    const patternPerformance: Array<{ pattern: string; performance: number; frequency: number }> = [];
    
    this.patternWeights.forEach((weight, pattern) => {
      const frequency = this.performanceData.filter(p => 
        this.extractPatterns(p).includes(pattern)
      ).length;
      
      patternPerformance.push({
        pattern,
        performance: weight,
        frequency
      });
    });
    
    return patternPerformance
      .sort((a, b) => a.performance - b.performance)
      .slice(0, 3)
      .map(p => ({
        ...p,
        confidence: Math.min((1 - p.performance) * 2, 1)
      }));
  }

  /**
   * Generate recommendations
   */
  private generateRecommendations(): MLInsights['recommendations'] {
    const recommendations: MLInsights['recommendations'] = [];
    
    // Pattern-based recommendations
    const topPatterns = this.getTopPerformingPatterns();
    if (topPatterns.length > 0) {
      recommendations.push({
        type: 'pattern',
        recommendation: `Focus on ${topPatterns[0].pattern} pattern for better performance`,
        expectedImprovement: topPatterns[0].performance * 0.2,
        confidence: topPatterns[0].confidence
      });
    }
    
    // Framework-based recommendations
    const topFrameworks = Array.from(this.frameworkPerformance.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3);
    
    if (topFrameworks.length > 0) {
      recommendations.push({
        type: 'framework',
        recommendation: `Use ${topFrameworks[0][0]} framework for optimal results`,
        expectedImprovement: topFrameworks[0][1] * 0.15,
        confidence: topFrameworks[0][1]
      });
    }
    
    // Style recommendations
    const avgPerformance = this.performanceData.reduce((sum, p) => 
      sum + this.calculatePerformanceScore(p.metrics), 0
    ) / this.performanceData.length;
    
    if (avgPerformance < 0.6) {
      recommendations.push({
        type: 'style',
        recommendation: 'Improve content style and emotional appeal',
        expectedImprovement: 0.2,
        confidence: 0.8
      });
    }
    
    // Timing recommendations
    if (this.performanceData.length > 10) {
      recommendations.push({
        type: 'timing',
        recommendation: 'Optimize posting times based on performance data',
        expectedImprovement: 0.1,
        confidence: 0.7
      });
    }
    
    return recommendations;
  }

  /**
   * Calculate model accuracy
   */
  private calculateModelAccuracy(): number {
    if (this.performanceData.length < 10) return 0.5;
    
    // Simple accuracy calculation based on prediction vs actual performance
    let correctPredictions = 0;
    let totalPredictions = 0;
    
    this.performanceData.forEach(performance => {
      const predictedPerformance = this.predictPerformance(performance);
      const actualPerformance = this.calculatePerformanceScore(performance.metrics);
      
      const predictionError = Math.abs(predictedPerformance - actualPerformance);
      if (predictionError < 0.2) {
        correctPredictions++;
      }
      totalPredictions++;
    });
    
    return totalPredictions > 0 ? correctPredictions / totalPredictions : 0.5;
  }

  /**
   * Calculate learning progress
   */
  private calculateLearningProgress(): number {
    const totalDataPoints = this.performanceData.length;
    const maxDataPoints = 1000;
    
    return Math.min(totalDataPoints / maxDataPoints, 1);
  }

  /**
   * Predict content performance
   */
  predictPerformance(content: Partial<ContentPerformance>): number {
    if (!content.headline || !content.subheadline || !content.caption || !content.cta) {
      return 0.5;
    }
    
    const patterns = this.extractPatterns(content as ContentPerformance);
    let prediction = 0.5;
    
    // Base prediction on pattern weights
    patterns.forEach(pattern => {
      const weight = this.patternWeights.get(pattern) || 0.5;
      prediction += weight * 0.1;
    });
    
    // Adjust for framework performance
    if (content.framework) {
      const frameworkPerf = this.frameworkPerformance.get(content.framework) || 0.5;
      prediction = (prediction + frameworkPerf) / 2;
    }
    
    // Adjust for business type insights
    if (content.businessContext?.businessType) {
      const businessInsights = this.businessTypeInsights.get(content.businessContext.businessType);
      if (businessInsights) {
        prediction = (prediction + businessInsights.avgPerformance) / 2;
      }
    }
    
    return Math.max(0, Math.min(1, prediction));
  }

  /**
   * Get ML prediction for new content
   */
  getMLPrediction(content: Partial<ContentPerformance>): MLPrediction {
    const predictedPerformance = this.predictPerformance(content);
    const confidence = this.calculateModelAccuracy();
    
    const reasoning: string[] = [];
    const riskFactors: string[] = [];
    
    // Generate reasoning based on patterns
    const patterns = this.extractPatterns(content as ContentPerformance);
    patterns.forEach(pattern => {
      const weight = this.patternWeights.get(pattern) || 0.5;
      if (weight > 0.7) {
        reasoning.push(`${pattern} pattern has high success rate`);
      } else if (weight < 0.3) {
        riskFactors.push(`${pattern} pattern has low success rate`);
      }
    });
    
    // Generate reasoning based on framework
    if (content.framework) {
      const frameworkPerf = this.frameworkPerformance.get(content.framework) || 0.5;
      if (frameworkPerf > 0.7) {
        reasoning.push(`${content.framework} framework performs well`);
      } else if (frameworkPerf < 0.3) {
        riskFactors.push(`${content.framework} framework has low performance`);
      }
    }
    
    return {
      contentId: content.contentId || 'unknown',
      predictedPerformance: {
        impressions: Math.round(predictedPerformance * 10000),
        clicks: Math.round(predictedPerformance * 1000),
        conversions: Math.round(predictedPerformance * 100),
        engagement: Math.round(predictedPerformance * 1000)
      },
      confidence,
      reasoning,
      riskFactors
    };
  }

  /**
   * Get model statistics
   */
  getModelStatistics(): any {
    return {
      totalDataPoints: this.performanceData.length,
      modelVersion: this.modelVersion,
      learningRate: this.learningRate,
      patternWeights: Object.fromEntries(this.patternWeights),
      frameworkPerformance: Object.fromEntries(this.frameworkPerformance),
      businessTypeInsights: Object.fromEntries(this.businessTypeInsights),
      locationInsights: Object.fromEntries(this.locationInsights),
      platformInsights: Object.fromEntries(this.platformInsights)
    };
  }
}

// Export singleton instance
export const mlContentOptimizer = new MLContentOptimizer({
  businessName: 'default',
  businessType: 'service',
  location: 'default',
  platform: 'social',
  learningRate: 0.1,
  modelVersion: '1.0.0'
});

export default MLContentOptimizer;

