/**
 * Multi-Framework Generator - Generates content using multiple frameworks in parallel
 * Implements parallel content generation with different approaches and selects the best result
 */

export interface FrameworkConfig {
  businessName: string;
  businessType: string;
  location: string;
  businessDetails: any;
  trendingData?: any;
  businessIntelligence?: any;
  wordPool?: any;
  creativityLevel: number;
}

export interface FrameworkResult {
  framework: string;
  headline: string;
  subheadline: string;
  caption: string;
  cta: string;
  score: number;
  reasoning: string;
  strengths: string[];
  weaknesses: string[];
}

export interface MultiFrameworkResult {
  bestResult: FrameworkResult;
  allResults: FrameworkResult[];
  recommendations: string[];
  insights: {
    topFrameworks: string[];
    commonStrengths: string[];
    improvementAreas: string[];
  };
}

export class MultiFrameworkGenerator {
  private frameworks = [
    'AIDA', 'PAS', 'BEFORE_AFTER_BRIDGE', 'STORYTELLING', 'SOCIAL_PROOF',
    'PROBLEM_SOLUTION', 'BENEFIT_FOCUSED', 'URGENCY_SCARCITY', 'CURIOSITY_HOOK',
    'AUTHORITY_POSITIONING', 'EMOTIONAL_CONNECTION', 'LOCAL_INSIDER'
  ];

  /**
   * Generate content using multiple frameworks in parallel
   */
  async generateMultiFrameworkContent(config: FrameworkConfig): Promise<MultiFrameworkResult> {
    const { businessName, businessType, location, businessDetails, trendingData, businessIntelligence, wordPool, creativityLevel } = config;

    // Select 3-5 frameworks to use in parallel
    const selectedFrameworks = this.selectFrameworks(businessType, creativityLevel);
    
    // Generate content using each framework in parallel
    const frameworkPromises = selectedFrameworks.map(framework => 
      this.generateWithFramework(framework, config)
    );

    try {
      const results = await Promise.all(frameworkPromises);
      
      // Score and rank all results
      const scoredResults = results.map(result => ({
        ...result,
        score: this.calculateFrameworkScore(result, config)
      }));

      // Sort by score (highest first)
      scoredResults.sort((a, b) => b.score - a.score);

      // Select the best result
      const bestResult = scoredResults[0];

      // Generate insights and recommendations
      const insights = this.generateInsights(scoredResults);
      const recommendations = this.generateRecommendations(bestResult, scoredResults, config);

      return {
        bestResult,
        allResults: scoredResults,
        recommendations,
        insights
      };
    } catch (error) {
      console.error('Multi-framework generation failed:', error);
      // Fallback to single framework
      return this.generateFallbackContent(config);
    }
  }

  /**
   * Select frameworks based on business type and creativity level
   */
  private selectFrameworks(businessType: string, creativityLevel: number): string[] {
    const frameworkMap: Record<string, string[]> = {
      'restaurant': ['STORYTELLING', 'SENSORY_APPEAL', 'LOCAL_INSIDER', 'SOCIAL_PROOF', 'EMOTIONAL_CONNECTION'],
      'bakery': ['STORYTELLING', 'SENSORY_APPEAL', 'NOSTALGIA', 'LOCAL_INSIDER', 'EMOTIONAL_CONNECTION'],
      'fitness': ['BEFORE_AFTER_BRIDGE', 'PROBLEM_SOLUTION', 'URGENCY_SCARCITY', 'SOCIAL_PROOF', 'MOTIVATION'],
      'beauty': ['BEFORE_AFTER_BRIDGE', 'EMOTIONAL_CONNECTION', 'SOCIAL_PROOF', 'AUTHORITY_POSITIONING', 'TRANSFORMATION'],
      'retail': ['URGENCY_SCARCITY', 'SOCIAL_PROOF', 'CURIOSITY_HOOK', 'BENEFIT_FOCUSED', 'EXCLUSIVITY'],
      'tech': ['AUTHORITY_POSITIONING', 'BENEFIT_FOCUSED', 'INNOVATION', 'PROBLEM_SOLUTION', 'FUTURE_FOCUSED'],
      'service': ['AUTHORITY_POSITIONING', 'SOCIAL_PROOF', 'PROBLEM_SOLUTION', 'TRUST_BUILDING', 'LOCAL_INSIDER']
    };

    const businessFrameworks = frameworkMap[businessType.toLowerCase()] || frameworkMap['service'];
    
    // Select 3-5 frameworks based on creativity level
    const numFrameworks = creativityLevel > 70 ? 5 : creativityLevel > 40 ? 4 : 3;
    
    // Shuffle and select
    const shuffled = businessFrameworks.sort(() => Math.random() - 0.5);
    return shuffled.slice(0, numFrameworks);
  }

  /**
   * Generate content using a specific framework
   */
  private async generateWithFramework(framework: string, config: FrameworkConfig): Promise<FrameworkResult> {
    const { businessName, businessType, location, businessDetails, trendingData, businessIntelligence, wordPool } = config;

    try {
      const prompt = this.buildFrameworkPrompt(framework, config);
      
      // Simulate AI call (replace with actual AI service)
      const response = await this.callAI(prompt);
      
      // Parse response
      const content = this.parseFrameworkResponse(response, framework);
      
      return {
        framework,
        headline: content.headline,
        subheadline: content.subheadline,
        caption: content.caption,
        cta: content.cta,
        score: 0, // Will be calculated later
        reasoning: content.reasoning || '',
        strengths: content.strengths || [],
        weaknesses: content.weaknesses || []
      };
    } catch (error) {
      console.error(`Framework ${framework} generation failed:`, error);
      return this.generateFallbackFrameworkResult(framework, config);
    }
  }

  /**
   * Build framework-specific prompt
   */
  private buildFrameworkPrompt(framework: string, config: FrameworkConfig): string {
    const { businessName, businessType, location, businessDetails, trendingData, businessIntelligence, wordPool } = config;

    const frameworkInstructions = this.getFrameworkInstructions(framework);
    
    return `
🎯 ${framework} FRAMEWORK CONTENT GENERATION

You are an expert copywriter using the ${framework} framework to create compelling content for ${businessName}, a ${businessType} in ${location}.

FRAMEWORK INSTRUCTIONS:
${frameworkInstructions}

BUSINESS CONTEXT:
- Business: ${businessName}
- Type: ${businessType}
- Location: ${location}
- Services: ${businessDetails.services || 'Not specified'}
- Target Audience: ${businessDetails.targetAudience || 'Local customers'}

${trendingData ? `TRENDING DATA: ${JSON.stringify(trendingData).slice(0, 200)}...` : ''}
${businessIntelligence ? `BUSINESS INTELLIGENCE: ${JSON.stringify(businessIntelligence).slice(0, 200)}...` : ''}
${wordPool ? `WORD POOL: ${JSON.stringify(wordPool).slice(0, 200)}...` : ''}

REQUIREMENTS:
- Create content that follows the ${framework} framework exactly
- Make it specific to ${businessName} and ${location}
- Use fresh, creative language
- Avoid repetitive patterns
- Include psychological triggers appropriate for this framework
- End with a compelling call-to-action

OUTPUT FORMAT:
{
  "headline": "Compelling headline following ${framework}",
  "subheadline": "Supporting subheadline",
  "caption": "Full social media caption",
  "cta": "Call-to-action",
  "reasoning": "Why this approach works for ${businessName}",
  "strengths": ["strength1", "strength2", "strength3"],
  "weaknesses": ["weakness1", "weakness2"]
}
`;
  }

  /**
   * Get framework-specific instructions
   */
  private getFrameworkInstructions(framework: string): string {
    const instructions: Record<string, string> = {
      'AIDA': `
        AIDA Framework (Attention, Interest, Desire, Action):
        - ATTENTION: Hook with compelling opening that grabs attention
        - INTEREST: Build curiosity and engagement with relevant information
        - DESIRE: Create want and need through benefits and emotional appeal
        - ACTION: Clear, compelling call-to-action that drives response
      `,
      'PAS': `
        PAS Framework (Problem, Agitation, Solution):
        - PROBLEM: Identify specific pain point your audience faces
        - AGITATION: Amplify the problem's impact and consequences
        - SOLUTION: Present your business as the clear answer
      `,
      'BEFORE_AFTER_BRIDGE': `
        Before-After-Bridge Framework:
        - BEFORE: Current frustrating situation or problem
        - AFTER: Desired outcome or transformation
        - BRIDGE: How your business gets them from before to after
      `,
      'STORYTELLING': `
        Storytelling Framework:
        - CHARACTER: Relatable person or situation
        - CONFLICT: Challenge or problem faced
        - RESOLUTION: How your business solved it
        - LESSON: Value and insight for the audience
      `,
      'SOCIAL_PROOF': `
        Social Proof Framework:
        - TESTIMONIAL: Customer success story or testimonial
        - STATISTICS: Numbers that build credibility
        - COMMUNITY: Show others like them using your service
        - VALIDATION: Why they should trust you
      `,
      'PROBLEM_SOLUTION': `
        Problem-Solution Framework:
        - PROBLEM: Specific pain point or challenge
        - SOLUTION: Your business as the answer
        - BENEFITS: What they gain from the solution
        - PROOF: Evidence that your solution works
      `,
      'BENEFIT_FOCUSED': `
        Benefit-Focused Framework:
        - BENEFITS: Lead with what customers get
        - FEATURES: Supporting details and specifications
        - VALUE: Why it's worth their time and money
        - URGENCY: Why they should act now
      `,
      'URGENCY_SCARCITY': `
        Urgency-Scarcity Framework:
        - URGENCY: Time-sensitive or limited-time offers
        - SCARCITY: Limited quantity or availability
        - FOMO: Fear of missing out on opportunity
        - ACTION: Clear next steps to secure the offer
      `,
      'CURIOSITY_HOOK': `
        Curiosity Hook Framework:
        - HOOK: Intriguing opening that creates curiosity
        - REVEAL: Gradually reveal information
        - ENGAGE: Keep audience interested and reading
        - RESOLVE: Provide satisfying conclusion
      `,
      'AUTHORITY_POSITIONING': `
        Authority Positioning Framework:
        - CREDIBILITY: Establish expertise and authority
        - INSIGHTS: Share valuable knowledge and insights
        - PROOF: Demonstrate expertise through examples
        - TRUST: Build confidence in your recommendations
      `,
      'EMOTIONAL_CONNECTION': `
        Emotional Connection Framework:
        - EMOTION: Tap into specific emotions (joy, fear, hope, etc.)
        - RELATABILITY: Make it relatable to their situation
        - EMPATHY: Show understanding of their feelings
        - INSPIRATION: Motivate them to take action
      `,
      'LOCAL_INSIDER': `
        Local Insider Framework:
        - LOCAL KNOWLEDGE: Use local insights and expertise
        - COMMUNITY: Connect with local community values
        - CULTURE: Incorporate local culture and language
        - BELONGING: Make them feel part of the community
      `
    };

    return instructions[framework] || instructions['AIDA'];
  }

  /**
   * Parse framework response
   */
  private parseFrameworkResponse(response: string, framework: string): any {
    try {
      return JSON.parse(response);
    } catch (error) {
      console.warn(`Failed to parse ${framework} response, using fallback`);
      return this.getFallbackFrameworkContent(framework);
    }
  }

  /**
   * Calculate framework score
   */
  private calculateFrameworkScore(result: FrameworkResult, config: FrameworkConfig): number {
    let score = 0;

    // Length appropriateness (not too short, not too long)
    const headlineLength = result.headline.length;
    const subheadlineLength = result.subheadline.length;
    const captionLength = result.caption.length;

    if (headlineLength >= 5 && headlineLength <= 15) score += 20;
    if (subheadlineLength >= 10 && subheadlineLength <= 25) score += 20;
    if (captionLength >= 50 && captionLength <= 200) score += 20;

    // Uniqueness (avoid repetitive words)
    const allText = `${result.headline} ${result.subheadline} ${result.caption}`;
    const words = allText.toLowerCase().split(/\s+/);
    const uniqueWords = new Set(words);
    const uniquenessRatio = uniqueWords.size / words.length;
    score += uniquenessRatio * 20;

    // Business relevance
    const businessName = config.businessName.toLowerCase();
    const businessType = config.businessType.toLowerCase();
    if (allText.toLowerCase().includes(businessName)) score += 10;
    if (allText.toLowerCase().includes(businessType)) score += 10;

    // Emotional impact
    const emotionalWords = ['amazing', 'incredible', 'outstanding', 'excellent', 'fantastic', 'wonderful', 'great', 'awesome'];
    const emotionalCount = emotionalWords.filter(word => allText.toLowerCase().includes(word)).length;
    score += Math.min(emotionalCount * 5, 20);

    // Call-to-action strength
    if (result.cta.length >= 3 && result.cta.length <= 15) score += 10;

    return Math.min(score, 100);
  }

  /**
   * Generate insights from all results
   */
  private generateInsights(results: FrameworkResult[]): MultiFrameworkResult['insights'] {
    const topFrameworks = results
      .sort((a, b) => b.score - a.score)
      .slice(0, 3)
      .map(r => r.framework);

    const allStrengths = results.flatMap(r => r.strengths);
    const strengthCounts = allStrengths.reduce((acc, strength) => {
      acc[strength] = (acc[strength] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const commonStrengths = Object.entries(strengthCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([strength]) => strength);

    const allWeaknesses = results.flatMap(r => r.weaknesses);
    const weaknessCounts = allWeaknesses.reduce((acc, weakness) => {
      acc[weakness] = (acc[weakness] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const improvementAreas = Object.entries(weaknessCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 3)
      .map(([weakness]) => weakness);

    return {
      topFrameworks,
      commonStrengths,
      improvementAreas
    };
  }

  /**
   * Generate recommendations
   */
  private generateRecommendations(
    bestResult: FrameworkResult,
    allResults: FrameworkResult[],
    config: FrameworkConfig
  ): string[] {
    const recommendations: string[] = [];

    // Framework-specific recommendations
    recommendations.push(`The ${bestResult.framework} framework scored highest (${bestResult.score}/100) for this content.`);

    // Strength-based recommendations
    if (bestResult.strengths.length > 0) {
      recommendations.push(`Key strengths: ${bestResult.strengths.join(', ')}`);
    }

    // Improvement recommendations
    if (bestResult.weaknesses.length > 0) {
      recommendations.push(`Areas for improvement: ${bestResult.weaknesses.join(', ')}`);
    }

    // Comparison recommendations
    const secondBest = allResults[1];
    if (secondBest && secondBest.score > 80) {
      recommendations.push(`Consider A/B testing with ${secondBest.framework} framework (score: ${secondBest.score})`);
    }

    // General recommendations
    recommendations.push('Use data-driven insights to optimize content performance');
    recommendations.push('Test different frameworks to find what works best for your audience');

    return recommendations;
  }

  /**
   * Simulate AI call (replace with actual AI service)
   */
  private async callAI(prompt: string): Promise<string> {
    // This would be replaced with actual AI service call
    // For now, return a mock response
    return JSON.stringify({
      headline: 'Revolutionary Business Solution',
      subheadline: 'Transform your business with our innovative approach',
      caption: 'Discover how our cutting-edge solutions can revolutionize your business operations and drive unprecedented growth.',
      cta: 'Get started today!',
      reasoning: 'This approach focuses on innovation and transformation',
      strengths: ['Innovative', 'Transformative', 'Growth-focused'],
      weaknesses: ['Generic', 'Could be more specific']
    });
  }

  /**
   * Generate fallback content when multi-framework fails
   */
  private generateFallbackContent(config: FrameworkConfig): MultiFrameworkResult {
    const fallbackResult: FrameworkResult = {
      framework: 'FALLBACK',
      headline: `${config.businessName} - Quality ${config.businessType}`,
      subheadline: `Professional ${config.businessType} services in ${config.location}`,
      caption: `Experience exceptional ${config.businessType} services with ${config.businessName}. We're committed to delivering excellence.`,
      cta: 'Contact us today!',
      score: 60,
      reasoning: 'Fallback content due to generation failure',
      strengths: ['Reliable', 'Professional'],
      weaknesses: ['Generic', 'Not optimized']
    };

    return {
      bestResult: fallbackResult,
      allResults: [fallbackResult],
      recommendations: ['Use fallback content', 'Retry generation with different parameters'],
      insights: {
        topFrameworks: ['FALLBACK'],
        commonStrengths: ['Reliable'],
        improvementAreas: ['Generic content']
      }
    };
  }

  /**
   * Generate fallback framework result
   */
  private generateFallbackFrameworkResult(framework: string, config: FrameworkConfig): FrameworkResult {
    return {
      framework,
      headline: `${config.businessName} - ${framework} Approach`,
      subheadline: `Professional ${config.businessType} services`,
      caption: `Experience quality ${config.businessType} services with ${config.businessName}.`,
      cta: 'Learn more!',
      score: 50,
      reasoning: `Fallback ${framework} content`,
      strengths: ['Available'],
      weaknesses: ['Generic', 'Not optimized']
    };
  }

  /**
   * Get fallback framework content
   */
  private getFallbackFrameworkContent(framework: string): any {
    return {
      headline: `Quality ${framework} Solution`,
      subheadline: 'Professional service delivery',
      caption: 'Experience excellence with our professional services.',
      cta: 'Contact us!',
      reasoning: `Fallback ${framework} content`,
      strengths: ['Professional'],
      weaknesses: ['Generic']
    };
  }
}

// Export singleton instance
export const multiFrameworkGenerator = new MultiFrameworkGenerator();

export default MultiFrameworkGenerator;

