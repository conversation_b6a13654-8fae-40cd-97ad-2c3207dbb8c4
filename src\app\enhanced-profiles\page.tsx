'use client';

/**
 * Enhanced Profiles Page
 * Main page for managing enhanced business profiles and accessing advanced features
 */

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Building, 
  BarChart3, 
  Zap, 
  ArrowRight, 
  Sparkles, 
  Globe,
  Users,
  TrendingUp,
  Info
} from 'lucide-react';
import { EnhancedBrandProvider, useEnhancedBrand } from '@/contexts/enhanced-brand-context';
import { EnhancedProfileManager } from '@/components/profile-management/enhanced-profile-manager';
import { ProfileAnalyticsDashboard } from '@/components/analytics/profile-analytics-dashboard';
import { EnhancedContentGenerator } from '@/components/content-generation/enhanced-content-generator';
import { ProfileMigrationTool } from '@/components/migration/profile-migration-tool';

function EnhancedProfilesContent() {
  const { 
    enhancedProfiles, 
    legacyProfiles, 
    currentEnhancedProfile, 
    loading 
  } = useEnhancedBrand();
  
  const [activeTab, setActiveTab] = useState('overview');

  const stats = {
    enhancedProfiles: enhancedProfiles.length,
    legacyProfiles: legacyProfiles.length,
    totalProfiles: enhancedProfiles.length + legacyProfiles.length,
    averageCompletion: enhancedProfiles.length > 0 
      ? Math.round(enhancedProfiles.reduce((sum, p) => sum + (p.metadata.completionScore || 0), 0) / enhancedProfiles.length)
      : 0
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="space-y-6">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/3 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[1, 2, 3].map((i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="pt-6">
                  <div className="space-y-3">
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold flex items-center gap-3">
          <Sparkles className="h-8 w-8 text-blue-500" />
          Enhanced Business Profiles
        </h1>
        <p className="text-muted-foreground text-lg">
          Advanced business profile management with comprehensive data collection and AI-powered content generation
        </p>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Enhanced Profiles</p>
                <p className="text-2xl font-bold">{stats.enhancedProfiles}</p>
              </div>
              <Building className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Legacy Profiles</p>
                <p className="text-2xl font-bold">{stats.legacyProfiles}</p>
              </div>
              <Users className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg. Completion</p>
                <p className="text-2xl font-bold">{stats.averageCompletion}%</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Profiles</p>
                <p className="text-2xl font-bold">{stats.totalProfiles}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Current Profile Status */}
      {currentEnhancedProfile && (
        <Alert>
          <Building className="h-4 w-4" />
          <AlertDescription>
            <strong>Active Profile:</strong> {currentEnhancedProfile.coreInfo.businessName} 
            <Badge className="ml-2" variant="secondary">
              {currentEnhancedProfile.metadata.completionScore}% Complete
            </Badge>
            {currentEnhancedProfile.contentPreferences.localizationToggle && (
              <Badge className="ml-2" variant="secondary">
                <Globe className="h-3 w-3 mr-1" />
                Localized
              </Badge>
            )}
          </AlertDescription>
        </Alert>
      )}

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="profiles">Manage Profiles</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="content">Content Generation</TabsTrigger>
          <TabsTrigger value="migration">Migration</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <div className="space-y-6">
            {/* Welcome Card */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Sparkles className="h-5 w-5 text-blue-500" />
                  Welcome to Enhanced Business Profiles
                </CardTitle>
                <CardDescription>
                  Comprehensive business data collection and AI-powered content generation framework
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h3 className="font-semibold">Key Features</h3>
                    <ul className="space-y-2 text-sm">
                      <li className="flex items-center gap-2">
                        <Zap className="h-4 w-4 text-blue-500" />
                        Advanced content generation with comprehensive business data
                      </li>
                      <li className="flex items-center gap-2">
                        <Globe className="h-4 w-4 text-green-500" />
                        Local language integration and cultural awareness
                      </li>
                      <li className="flex items-center gap-2">
                        <BarChart3 className="h-4 w-4 text-purple-500" />
                        Quality validation and improvement suggestions
                      </li>
                      <li className="flex items-center gap-2">
                        <TrendingUp className="h-4 w-4 text-orange-500" />
                        Competitive differentiation tracking
                      </li>
                    </ul>
                  </div>
                  
                  <div className="space-y-4">
                    <h3 className="font-semibold">Quick Actions</h3>
                    <div className="space-y-2">
                      {enhancedProfiles.length === 0 ? (
                        <Button 
                          onClick={() => setActiveTab('profiles')} 
                          className="w-full justify-start"
                        >
                          <Building className="h-4 w-4 mr-2" />
                          Create Your First Enhanced Profile
                        </Button>
                      ) : (
                        <>
                          <Button 
                            onClick={() => setActiveTab('content')} 
                            className="w-full justify-start"
                          >
                            <Zap className="h-4 w-4 mr-2" />
                            Generate Enhanced Content
                          </Button>
                          <Button 
                            onClick={() => setActiveTab('analytics')} 
                            variant="outline"
                            className="w-full justify-start"
                          >
                            <BarChart3 className="h-4 w-4 mr-2" />
                            View Profile Analytics
                          </Button>
                        </>
                      )}
                      
                      {legacyProfiles.length > 0 && (
                        <Button 
                          onClick={() => setActiveTab('migration')} 
                          variant="outline"
                          className="w-full justify-start"
                        >
                          <ArrowRight className="h-4 w-4 mr-2" />
                          Migrate Legacy Profiles
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Status Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Enhanced Profiles Status */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Enhanced Profiles</CardTitle>
                  <CardDescription>Your comprehensive business profiles</CardDescription>
                </CardHeader>
                <CardContent>
                  {enhancedProfiles.length > 0 ? (
                    <div className="space-y-3">
                      {enhancedProfiles.slice(0, 3).map((profile) => (
                        <div key={profile.metadata.profileId} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                          <div>
                            <p className="font-medium text-sm">{profile.coreInfo.businessName}</p>
                            <p className="text-xs text-muted-foreground">{profile.coreInfo.industryCategory}</p>
                          </div>
                          <Badge variant="secondary">
                            {profile.metadata.completionScore}%
                          </Badge>
                        </div>
                      ))}
                      {enhancedProfiles.length > 3 && (
                        <p className="text-sm text-muted-foreground text-center">
                          +{enhancedProfiles.length - 3} more profiles
                        </p>
                      )}
                    </div>
                  ) : (
                    <div className="text-center py-4">
                      <Building className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                      <p className="text-sm text-muted-foreground">No enhanced profiles yet</p>
                      <Button 
                        onClick={() => setActiveTab('profiles')} 
                        size="sm" 
                        className="mt-2"
                      >
                        Create Profile
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Migration Status */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Migration Status</CardTitle>
                  <CardDescription>Legacy profile upgrade progress</CardDescription>
                </CardHeader>
                <CardContent>
                  {legacyProfiles.length > 0 ? (
                    <div className="space-y-3">
                      <Alert>
                        <Info className="h-4 w-4" />
                        <AlertDescription>
                          You have {legacyProfiles.length} legacy profile{legacyProfiles.length > 1 ? 's' : ''} that can be upgraded to the enhanced format.
                        </AlertDescription>
                      </Alert>
                      <Button 
                        onClick={() => setActiveTab('migration')} 
                        className="w-full"
                        variant="outline"
                      >
                        <ArrowRight className="h-4 w-4 mr-2" />
                        Start Migration
                      </Button>
                    </div>
                  ) : (
                    <div className="text-center py-4">
                      <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                        <Building className="h-4 w-4 text-green-600" />
                      </div>
                      <p className="text-sm text-muted-foreground">All profiles are enhanced!</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="profiles">
          <EnhancedProfileManager />
        </TabsContent>

        <TabsContent value="analytics">
          <ProfileAnalyticsDashboard />
        </TabsContent>

        <TabsContent value="content">
          <EnhancedContentGenerator />
        </TabsContent>

        <TabsContent value="migration">
          <ProfileMigrationTool 
            onMigrationComplete={() => setActiveTab('profiles')}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default function EnhancedProfilesPage() {
  return (
    <EnhancedBrandProvider>
      <EnhancedProfilesContent />
    </EnhancedBrandProvider>
  );
}
