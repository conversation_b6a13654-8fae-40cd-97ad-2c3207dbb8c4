/**
 * Enhanced Profiles Integration Tests
 * Comprehensive testing of all integrated components and API endpoints
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { enhancedBusinessProfileService } from '@/lib/supabase/services/enhanced-business-profile-service';
import { Revo15EnhancedContentFramework } from '@/ai/revo-1.5-enhanced-content-framework';
import { LocalizationEngine } from '@/ai/localization-engine';
import { ContentQualityValidator } from '@/ai/content-quality-validator';
import type { EnhancedBusinessProfile } from '@/lib/types/enhanced-business-profile';

// Mock data
const mockEnhancedProfile: EnhancedBusinessProfile = {
  coreInfo: {
    businessName: 'Test Business',
    industryCategory: 'Tech',
    businessModel: 'B2B',
    primaryLocation: {
      city: 'Nairobi',
      state: '',
      country: 'Kenya'
    },
    establishmentYear: 2020,
    businessSize: 'Small (2-10)'
  },
  offerings: [
    {
      id: 'test-offering-1',
      name: 'Web Development',
      description: 'Custom web development services',
      pricingStructure: {
        type: 'Fixed',
        currency: 'USD',
        basePrice: 1000
      },
      deliveryTimeline: {
        estimatedTime: '2-3 weeks'
      },
      fulfillmentMethod: 'Digital delivery',
      targetCustomerProfile: {
        demographics: ['Small businesses'],
        painPoints: ['Need online presence']
      },
      primaryBenefits: ['Professional website', 'Increased visibility'],
      isActive: true
    }
  ],
  competitiveDifferentiation: {
    speedAdvantage: 'Faster delivery than competitors',
    qualityMetrics: 'High-quality code standards',
    pricingPosition: 'Competitive pricing'
  },
  trustIndicators: {
    businessLongevity: 4,
    customerBase: {
      customersServed: 50
    },
    reviewScores: {
      google: 4.8
    }
  },
  operationalParams: {
    operatingHours: {
      timezone: 'EAT'
    },
    serviceCoverage: {
      geographicBoundaries: ['Kenya', 'East Africa'],
      deliveryZones: ['Remote']
    },
    contactChannels: {
      email: '<EMAIL>',
      phone: '+254700000000',
      website: 'https://testbusiness.com'
    }
  },
  contentPreferences: {
    localizationToggle: true,
    contentTone: 'Professional',
    brandVoice: 'Expert and approachable',
    contentThemes: ['Technology', 'Innovation'],
    visualStyle: 'Modern and clean'
  },
  metadata: {
    profileId: 'test-profile-1',
    userId: 'test-user-1',
    createdAt: new Date(),
    updatedAt: new Date(),
    completionScore: 85,
    isActive: true
  }
};

describe('Enhanced Business Profile Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Profile Management', () => {
    it('should save enhanced profile successfully', async () => {
      const mockSave = jest.spyOn(enhancedBusinessProfileService, 'saveEnhancedProfile')
        .mockResolvedValue('test-profile-1');

      const result = await enhancedBusinessProfileService.saveEnhancedProfile(mockEnhancedProfile);

      expect(mockSave).toHaveBeenCalledWith(mockEnhancedProfile);
      expect(result).toBe('test-profile-1');
    });

    it('should load user enhanced profiles', async () => {
      const mockLoad = jest.spyOn(enhancedBusinessProfileService, 'loadUserEnhancedProfiles')
        .mockResolvedValue([mockEnhancedProfile]);

      const result = await enhancedBusinessProfileService.loadUserEnhancedProfiles('test-user-1');

      expect(mockLoad).toHaveBeenCalledWith('test-user-1');
      expect(result).toHaveLength(1);
      expect(result[0].coreInfo.businessName).toBe('Test Business');
    });

    it('should delete enhanced profile', async () => {
      const mockDelete = jest.spyOn(enhancedBusinessProfileService, 'deleteEnhancedProfile')
        .mockResolvedValue();

      await enhancedBusinessProfileService.deleteEnhancedProfile('test-profile-1', 'test-user-1');

      expect(mockDelete).toHaveBeenCalledWith('test-profile-1', 'test-user-1');
    });

    it('should convert legacy profile to enhanced format', async () => {
      const legacyProfile = {
        id: 'legacy-1',
        businessName: 'Legacy Business',
        businessType: 'Technology',
        location: 'Nairobi, Kenya',
        description: 'A legacy business profile',
        services: [{ name: 'Web Dev', description: 'Web development' }],
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const mockConvert = jest.spyOn(enhancedBusinessProfileService, 'convertLegacyProfile')
        .mockResolvedValue(mockEnhancedProfile);

      const result = await enhancedBusinessProfileService.convertLegacyProfile(legacyProfile, 'test-user-1');

      expect(mockConvert).toHaveBeenCalledWith(legacyProfile, 'test-user-1');
      expect(result.coreInfo.businessName).toBe('Test Business');
    });
  });

  describe('Profile Validation', () => {
    it('should calculate completion score correctly', () => {
      const score = enhancedBusinessProfileService.calculateCompletionScore(mockEnhancedProfile);
      expect(score).toBeGreaterThanOrEqual(80);
    });

    it('should validate required fields', () => {
      const incompleteProfile = { ...mockEnhancedProfile };
      incompleteProfile.coreInfo.businessName = '';

      expect(() => {
        enhancedBusinessProfileService.validateProfile(incompleteProfile);
      }).toThrow('Business name is required');
    });
  });
});

describe('Enhanced Content Generation Framework', () => {
  let framework: Revo15EnhancedContentFramework;

  beforeEach(() => {
    framework = new Revo15EnhancedContentFramework();
  });

  describe('Content Generation', () => {
    it('should generate enhanced content with business profile data', async () => {
      const mockGenerate = jest.spyOn(framework, 'generateEnhancedContent')
        .mockResolvedValue({
          headline: 'Professional Web Development Services',
          subheadline: 'Custom solutions for your business',
          caption: 'Transform your business with professional web development...',
          callToAction: 'Get your quote today!',
          hashtags: ['#WebDevelopment', '#Kenya', '#TechSolutions'],
          qualityScore: 88,
          improvementSuggestions: [],
          localizationApplied: true,
          dataSourcesUsed: ['Business Profile', 'Local Market Data']
        });

      const context = {
        businessProfile: mockEnhancedProfile,
        platform: 'instagram' as const,
        contentType: 'promotional' as const,
        useLocalization: true
      };

      const result = await framework.generateEnhancedContent(context);

      expect(mockGenerate).toHaveBeenCalledWith(context);
      expect(result.headline).toBe('Professional Web Development Services');
      expect(result.qualityScore).toBeGreaterThanOrEqual(80);
      expect(result.localizationApplied).toBe(true);
    });

    it('should validate business profile completeness', () => {
      const incompleteProfile = { ...mockEnhancedProfile };
      incompleteProfile.metadata.completionScore = 30;

      const isValid = framework.validateProfileCompleteness(incompleteProfile);
      expect(isValid).toBe(false);
    });

    it('should gather external data sources', async () => {
      const mockGatherData = jest.spyOn(framework, 'gatherExternalData')
        .mockResolvedValue({
          rssFeeds: ['Tech news from Kenya'],
          trendingTopics: ['AI development', 'Digital transformation'],
          localMarketData: ['Growing tech sector in Nairobi']
        });

      const result = await framework.gatherExternalData(mockEnhancedProfile);

      expect(mockGatherData).toHaveBeenCalledWith(mockEnhancedProfile);
      expect(result.rssFeeds).toHaveLength(1);
      expect(result.trendingTopics).toHaveLength(2);
    });
  });
});

describe('Localization Engine', () => {
  let localizationEngine: LocalizationEngine;

  beforeEach(() => {
    localizationEngine = new LocalizationEngine();
  });

  describe('Regional Support', () => {
    it('should apply Kenyan localization', () => {
      const content = {
        headline: 'Professional Services',
        caption: 'We provide excellent services for your business needs.',
        hashtags: ['#Professional', '#Services']
      };

      const localized = localizationEngine.applyLocalization(content, 'Kenya');

      expect(localized.localizationApplied).toBe(true);
      expect(localized.localPhrases).toContain('Karibu');
      expect(localized.culturalReferences).toHaveLength(1);
    });

    it('should apply Nigerian localization', () => {
      const content = {
        headline: 'Quality Products',
        caption: 'Best products for your family.',
        hashtags: ['#Quality', '#Products']
      };

      const localized = localizationEngine.applyLocalization(content, 'Nigeria');

      expect(localized.localizationApplied).toBe(true);
      expect(localized.localPhrases).toContain('Oga');
      expect(localized.culturalReferences).toHaveLength(1);
    });

    it('should handle unsupported regions gracefully', () => {
      const content = {
        headline: 'Test Content',
        caption: 'Test caption',
        hashtags: ['#Test']
      };

      const localized = localizationEngine.applyLocalization(content, 'Unsupported Country');

      expect(localized.localizationApplied).toBe(false);
      expect(localized.localPhrases).toHaveLength(0);
    });
  });
});

describe('Content Quality Validator', () => {
  let validator: ContentQualityValidator;

  beforeEach(() => {
    validator = new ContentQualityValidator();
  });

  describe('Quality Standards', () => {
    it('should validate content specificity', () => {
      const specificContent = {
        headline: 'Save 30% on Web Development',
        caption: 'Professional websites delivered in 2 weeks with 24/7 support',
        businessProfile: mockEnhancedProfile
      };

      const result = validator.validateSpecificity(specificContent);

      expect(result.score).toBeGreaterThanOrEqual(80);
      expect(result.hasConcreteNumbers).toBe(true);
      expect(result.hasTimeframes).toBe(true);
    });

    it('should validate content authenticity', () => {
      const authenticContent = {
        headline: 'Award-Winning Services',
        caption: 'Serving 50+ clients with 4.8-star Google rating',
        businessProfile: mockEnhancedProfile
      };

      const result = validator.validateAuthenticity(authenticContent);

      expect(result.score).toBeGreaterThanOrEqual(80);
      expect(result.verifiableClaims).toHaveLength(2);
    });

    it('should validate content relevance', () => {
      const relevantContent = {
        headline: 'Tech Solutions for Kenya',
        caption: 'Custom web development for Kenyan businesses',
        businessProfile: mockEnhancedProfile,
        externalData: {
          rssFeeds: ['Kenya tech growth'],
          trendingTopics: ['Digital transformation']
        }
      };

      const result = validator.validateRelevance(relevantContent);

      expect(result.score).toBeGreaterThanOrEqual(70);
      expect(result.relevantDataSources).toHaveLength(2);
    });

    it('should provide improvement suggestions', () => {
      const lowQualityContent = {
        headline: 'Good Services',
        caption: 'We provide services',
        businessProfile: mockEnhancedProfile
      };

      const result = validator.generateImprovementPlan(lowQualityContent);

      expect(result.overallScore).toBeLessThan(60);
      expect(result.prioritizedActions).toHaveLength(3);
      expect(result.estimatedImpact).toBeGreaterThan(0);
    });
  });
});

describe('API Endpoints', () => {
  describe('Enhanced Business Profiles API', () => {
    it('should handle profile creation', async () => {
      const mockFetch = jest.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ profileId: 'test-profile-1' })
      });
      global.fetch = mockFetch;

      const response = await fetch('/api/enhanced-business-profiles', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(mockEnhancedProfile)
      });

      const result = await response.json();

      expect(mockFetch).toHaveBeenCalledWith('/api/enhanced-business-profiles', expect.any(Object));
      expect(result.profileId).toBe('test-profile-1');
    });

    it('should handle profile updates', async () => {
      const mockFetch = jest.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ success: true })
      });
      global.fetch = mockFetch;

      const updates = { coreInfo: { businessName: 'Updated Business' } };

      const response = await fetch('/api/enhanced-business-profiles', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ profileId: 'test-profile-1', updates })
      });

      const result = await response.json();

      expect(mockFetch).toHaveBeenCalled();
      expect(result.success).toBe(true);
    });
  });

  describe('Enhanced Content Generation API', () => {
    it('should generate content with enhanced profile', async () => {
      const mockFetch = jest.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          content: {
            headline: 'Professional Web Development',
            caption: 'Custom solutions for your business',
            qualityScore: 85,
            localizationApplied: true
          }
        })
      });
      global.fetch = mockFetch;

      const requestBody = {
        profileId: 'test-profile-1',
        platform: 'instagram',
        contentType: 'promotional',
        useLocalization: true
      };

      const response = await fetch('/api/enhanced-content-generation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      });

      const result = await response.json();

      expect(mockFetch).toHaveBeenCalled();
      expect(result.content.qualityScore).toBeGreaterThanOrEqual(80);
      expect(result.content.localizationApplied).toBe(true);
    });

    it('should handle profile completion validation', async () => {
      const mockFetch = jest.fn().mockResolvedValue({
        ok: false,
        status: 400,
        json: () => Promise.resolve({
          error: 'Profile completion too low',
          completionScore: 45,
          requiredScore: 50
        })
      });
      global.fetch = mockFetch;

      const response = await fetch('/api/enhanced-content-generation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ profileId: 'incomplete-profile' })
      });

      expect(response.ok).toBe(false);
      expect(response.status).toBe(400);
    });
  });
});

describe('Integration Tests', () => {
  it('should complete full workflow: create profile -> generate content -> analyze quality', async () => {
    // Mock the entire workflow
    const mockSaveProfile = jest.spyOn(enhancedBusinessProfileService, 'saveEnhancedProfile')
      .mockResolvedValue('workflow-profile-1');

    const mockFramework = new Revo15EnhancedContentFramework();
    const mockGenerateContent = jest.spyOn(mockFramework, 'generateEnhancedContent')
      .mockResolvedValue({
        headline: 'Professional Services in Nairobi',
        subheadline: 'Karibu to quality solutions',
        caption: 'Transform your business with our expert services...',
        callToAction: 'Contact us today!',
        hashtags: ['#Nairobi', '#Professional', '#Kenya'],
        qualityScore: 92,
        improvementSuggestions: [],
        localizationApplied: true,
        dataSourcesUsed: ['Business Profile', 'Local Market Data', 'RSS Feeds']
      });

    // Step 1: Create enhanced profile
    const profileId = await enhancedBusinessProfileService.saveEnhancedProfile(mockEnhancedProfile);
    expect(profileId).toBe('workflow-profile-1');

    // Step 2: Generate content
    const content = await mockFramework.generateEnhancedContent({
      businessProfile: mockEnhancedProfile,
      platform: 'instagram',
      contentType: 'promotional',
      useLocalization: true
    });

    expect(content.qualityScore).toBeGreaterThanOrEqual(90);
    expect(content.localizationApplied).toBe(true);
    expect(content.dataSourcesUsed).toContain('Business Profile');

    // Step 3: Validate quality
    const validator = new ContentQualityValidator();
    const qualityReport = validator.generateImprovementPlan({
      headline: content.headline,
      caption: content.caption,
      businessProfile: mockEnhancedProfile
    });

    expect(qualityReport.overallScore).toBeGreaterThanOrEqual(85);
  });
});

// Cleanup
afterEach(() => {
  jest.restoreAllMocks();
});
