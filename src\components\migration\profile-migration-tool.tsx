'use client';

/**
 * Profile Migration Tool
 * Migrates legacy brand profiles to enhanced business profiles
 */

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, ArrowRight, AlertTriangle, Info, Zap } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useEnhancedBrand } from '@/contexts/enhanced-brand-context';
import type { CompleteBrandProfile } from '@/components/cbrand/cbrand-wizard';
import type { EnhancedBusinessProfile } from '@/lib/types/enhanced-business-profile';

interface ProfileMigrationToolProps {
  onMigrationComplete?: (enhancedProfile: EnhancedBusinessProfile) => void;
}

export function ProfileMigrationTool({ onMigrationComplete }: ProfileMigrationToolProps) {
  const { toast } = useToast();
  const { 
    legacyProfiles, 
    enhancedProfiles, 
    migrateToEnhanced, 
    migrating, 
    getProfileCompletionScore 
  } = useEnhancedBrand();
  
  const [selectedProfile, setSelectedProfile] = useState<CompleteBrandProfile | null>(null);
  const [migrationStep, setMigrationStep] = useState<'select' | 'preview' | 'migrating' | 'complete'>('select');
  const [migratedProfile, setMigratedProfile] = useState<EnhancedBusinessProfile | null>(null);

  // Filter out profiles that have already been migrated
  const migratedProfileNames = enhancedProfiles.map(p => p.coreInfo.businessName.toLowerCase());
  const availableForMigration = legacyProfiles.filter(
    profile => !migratedProfileNames.includes(profile.businessName.toLowerCase())
  );

  const handleSelectProfile = (profile: CompleteBrandProfile) => {
    setSelectedProfile(profile);
    setMigrationStep('preview');
  };

  const handleStartMigration = async () => {
    if (!selectedProfile) return;

    setMigrationStep('migrating');
    
    try {
      console.log('🔄 [Migration Tool] Starting migration for:', selectedProfile.businessName);
      
      const enhancedProfile = await migrateToEnhanced(selectedProfile);
      setMigratedProfile(enhancedProfile);
      setMigrationStep('complete');
      
      toast({
        title: "Migration Successful!",
        description: `${selectedProfile.businessName} has been upgraded to an enhanced business profile.`
      });

      onMigrationComplete?.(enhancedProfile);
      
    } catch (error) {
      console.error('❌ [Migration Tool] Migration failed:', error);
      setMigrationStep('preview');
      
      toast({
        title: "Migration Failed",
        description: error instanceof Error ? error.message : "Failed to migrate profile. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleStartOver = () => {
    setSelectedProfile(null);
    setMigratedProfile(null);
    setMigrationStep('select');
  };

  const renderMigrationPreview = (profile: CompleteBrandProfile) => {
    const currentScore = getProfileCompletionScore(profile);
    const estimatedEnhancedScore = Math.min(100, currentScore + 30); // Estimate improvement

    return (
      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Current Profile */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Current Profile</CardTitle>
              <CardDescription>Legacy brand profile</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm">Completion Score</span>
                <Badge variant="secondary">{currentScore}%</Badge>
              </div>
              <Progress value={currentScore} className="h-2" />
              
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Business Name</span>
                  <CheckCircle className="h-4 w-4 text-green-500" />
                </div>
                <div className="flex justify-between">
                  <span>Business Type</span>
                  <CheckCircle className="h-4 w-4 text-green-500" />
                </div>
                <div className="flex justify-between">
                  <span>Location</span>
                  {profile.location ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <AlertTriangle className="h-4 w-4 text-yellow-500" />
                  )}
                </div>
                <div className="flex justify-between">
                  <span>Services</span>
                  {profile.services && profile.services.length > 0 ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <AlertTriangle className="h-4 w-4 text-yellow-500" />
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Enhanced Profile Preview */}
          <Card className="border-blue-200 bg-blue-50">
            <CardHeader>
              <CardTitle className="text-sm flex items-center gap-2">
                Enhanced Profile
                <Zap className="h-4 w-4 text-blue-500" />
              </CardTitle>
              <CardDescription>After migration</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm">Estimated Score</span>
                <Badge className="bg-blue-500">{estimatedEnhancedScore}%</Badge>
              </div>
              <Progress value={estimatedEnhancedScore} className="h-2" />
              
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Core Information</span>
                  <CheckCircle className="h-4 w-4 text-green-500" />
                </div>
                <div className="flex justify-between">
                  <span>Products/Services</span>
                  <CheckCircle className="h-4 w-4 text-green-500" />
                </div>
                <div className="flex justify-between">
                  <span>Contact Channels</span>
                  <CheckCircle className="h-4 w-4 text-green-500" />
                </div>
                <div className="flex justify-between">
                  <span>Content Preferences</span>
                  <CheckCircle className="h-4 w-4 text-green-500" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Migration Benefits */}
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            <strong>Migration Benefits:</strong>
            <ul className="mt-2 space-y-1 text-sm">
              <li>• Enhanced content generation with comprehensive business data</li>
              <li>• Local language integration and cultural awareness</li>
              <li>• Advanced quality validation and improvement suggestions</li>
              <li>• Detailed competitive differentiation tracking</li>
              <li>• Trust indicators and social proof management</li>
            </ul>
          </AlertDescription>
        </Alert>
      </div>
    );
  };

  if (availableForMigration.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Profile Migration</CardTitle>
          <CardDescription>Upgrade your brand profiles to the enhanced format</CardDescription>
        </CardHeader>
        <CardContent>
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              {legacyProfiles.length === 0 
                ? "No legacy profiles found. All your profiles are already using the enhanced format!"
                : "All your legacy profiles have already been migrated to the enhanced format."
              }
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Profile Migration Tool</CardTitle>
          <CardDescription>
            Upgrade your legacy brand profiles to the enhanced business profile format for better content generation
          </CardDescription>
        </CardHeader>
      </Card>

      {migrationStep === 'select' && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Select Profile to Migrate</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {availableForMigration.map((profile) => (
              <Card 
                key={profile.id} 
                className="cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => handleSelectProfile(profile)}
              >
                <CardHeader>
                  <CardTitle className="text-base">{profile.businessName}</CardTitle>
                  <CardDescription>{profile.businessType}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">
                      Completion: {getProfileCompletionScore(profile)}%
                    </span>
                    <ArrowRight className="h-4 w-4 text-muted-foreground" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {migrationStep === 'preview' && selectedProfile && (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Migration Preview</h3>
            <Button variant="outline" onClick={handleStartOver}>
              Back to Selection
            </Button>
          </div>
          
          <Card>
            <CardHeader>
              <CardTitle>{selectedProfile.businessName}</CardTitle>
              <CardDescription>Preview the migration to enhanced format</CardDescription>
            </CardHeader>
            <CardContent>
              {renderMigrationPreview(selectedProfile)}
            </CardContent>
          </Card>

          <div className="flex justify-end space-x-4">
            <Button variant="outline" onClick={handleStartOver}>
              Cancel
            </Button>
            <Button onClick={handleStartMigration} disabled={migrating}>
              {migrating ? 'Migrating...' : 'Start Migration'}
            </Button>
          </div>
        </div>
      )}

      {migrationStep === 'migrating' && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
              <h3 className="text-lg font-semibold">Migrating Profile...</h3>
              <p className="text-muted-foreground">
                Converting {selectedProfile?.businessName} to enhanced format
              </p>
              <Progress value={75} className="w-full" />
            </div>
          </CardContent>
        </Card>
      )}

      {migrationStep === 'complete' && migratedProfile && (
        <div className="space-y-6">
          <Card className="border-green-200 bg-green-50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-green-700">
                <CheckCircle className="h-5 w-5" />
                Migration Complete!
              </CardTitle>
              <CardDescription>
                {migratedProfile.coreInfo.businessName} has been successfully upgraded
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Business Name:</span>
                    <p>{migratedProfile.coreInfo.businessName}</p>
                  </div>
                  <div>
                    <span className="font-medium">Industry:</span>
                    <p>{migratedProfile.coreInfo.industryCategory}</p>
                  </div>
                  <div>
                    <span className="font-medium">Location:</span>
                    <p>{migratedProfile.coreInfo.primaryLocation.city}, {migratedProfile.coreInfo.primaryLocation.country}</p>
                  </div>
                  <div>
                    <span className="font-medium">Completion Score:</span>
                    <Badge className="bg-green-500">{migratedProfile.metadata.completionScore}%</Badge>
                  </div>
                </div>

                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    Your profile has been migrated successfully! You can now:
                    <ul className="mt-2 space-y-1">
                      <li>• Complete additional profile sections for better content generation</li>
                      <li>• Enable localization for local language integration</li>
                      <li>• Use the enhanced content generation features</li>
                    </ul>
                  </AlertDescription>
                </Alert>
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-center space-x-4">
            <Button variant="outline" onClick={handleStartOver}>
              Migrate Another Profile
            </Button>
            <Button onClick={() => onMigrationComplete?.(migratedProfile)}>
              Continue with Enhanced Profile
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
