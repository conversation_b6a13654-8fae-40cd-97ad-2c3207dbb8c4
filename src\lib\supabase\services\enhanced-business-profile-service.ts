/**
 * Enhanced Business Profile Service for Supabase
 * Handles comprehensive business profile data storage and retrieval
 */

import { createClient } from '@supabase/supabase-js';
import type { EnhancedBusinessProfile, ProductService } from '@/lib/types/enhanced-business-profile';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

export class EnhancedBusinessProfileService {
  private supabase = createClient(supabaseUrl, supabaseAnonKey);

  /**
   * Save enhanced business profile
   */
  async saveEnhancedProfile(profile: EnhancedBusinessProfile): Promise<string> {
    try {
      console.log('💾 [Enhanced Profile Service] Saving enhanced business profile:', profile.coreInfo.businessName);

      // Validate required fields
      this.validateProfile(profile);

      // Calculate completion score
      const completionScore = this.calculateCompletionScore(profile);
      profile.metadata.completionScore = completionScore;

      // Insert or update profile
      const { data, error } = await this.supabase
        .from('enhanced_business_profiles')
        .upsert({
          id: profile.metadata.profileId,
          user_id: profile.metadata.userId,
          core_info: profile.coreInfo,
          offerings: profile.offerings,
          competitive_differentiation: profile.competitiveDifferentiation,
          trust_indicators: profile.trustIndicators,
          operational_params: profile.operationalParams,
          content_preferences: profile.contentPreferences,
          metadata: profile.metadata,
          created_at: profile.metadata.createdAt,
          updated_at: new Date()
        })
        .select('id')
        .single();

      if (error) {
        console.error('❌ [Enhanced Profile Service] Save error:', error);
        throw new Error(`Failed to save enhanced profile: ${error.message}`);
      }

      console.log('✅ [Enhanced Profile Service] Profile saved successfully:', data.id);
      return data.id;

    } catch (error) {
      console.error('❌ [Enhanced Profile Service] Critical error:', error);
      throw error;
    }
  }

  /**
   * Load enhanced business profile by ID
   */
  async loadEnhancedProfile(profileId: string, userId: string): Promise<EnhancedBusinessProfile | null> {
    try {
      console.log('📖 [Enhanced Profile Service] Loading profile:', profileId);

      const { data, error } = await this.supabase
        .from('enhanced_business_profiles')
        .select('*')
        .eq('id', profileId)
        .eq('user_id', userId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          console.log('ℹ️ [Enhanced Profile Service] Profile not found:', profileId);
          return null;
        }
        throw new Error(`Failed to load enhanced profile: ${error.message}`);
      }

      // Transform database record to EnhancedBusinessProfile
      const profile: EnhancedBusinessProfile = {
        coreInfo: data.core_info,
        offerings: data.offerings,
        competitiveDifferentiation: data.competitive_differentiation,
        trustIndicators: data.trust_indicators,
        operationalParams: data.operational_params,
        contentPreferences: data.content_preferences,
        metadata: {
          ...data.metadata,
          createdAt: new Date(data.created_at),
          updatedAt: new Date(data.updated_at)
        }
      };

      console.log('✅ [Enhanced Profile Service] Profile loaded:', profile.coreInfo.businessName);
      return profile;

    } catch (error) {
      console.error('❌ [Enhanced Profile Service] Load error:', error);
      throw error;
    }
  }

  /**
   * Load all enhanced profiles for a user
   */
  async loadUserEnhancedProfiles(userId: string): Promise<EnhancedBusinessProfile[]> {
    try {
      console.log('📖 [Enhanced Profile Service] Loading user profiles:', userId);

      const { data, error } = await this.supabase
        .from('enhanced_business_profiles')
        .select('*')
        .eq('user_id', userId)
        .eq('metadata->isActive', true)
        .order('updated_at', { ascending: false });

      if (error) {
        throw new Error(`Failed to load user profiles: ${error.message}`);
      }

      const profiles: EnhancedBusinessProfile[] = data.map(record => ({
        coreInfo: record.core_info,
        offerings: record.offerings,
        competitiveDifferentiation: record.competitive_differentiation,
        trustIndicators: record.trust_indicators,
        operationalParams: record.operational_params,
        contentPreferences: record.content_preferences,
        metadata: {
          ...record.metadata,
          createdAt: new Date(record.created_at),
          updatedAt: new Date(record.updated_at)
        }
      }));

      console.log(`✅ [Enhanced Profile Service] Loaded ${profiles.length} profiles for user`);
      return profiles;

    } catch (error) {
      console.error('❌ [Enhanced Profile Service] Load user profiles error:', error);
      throw error;
    }
  }

  /**
   * Delete enhanced business profile
   */
  async deleteEnhancedProfile(profileId: string, userId: string): Promise<boolean> {
    try {
      console.log('🗑️ [Enhanced Profile Service] Deleting profile:', profileId);

      const { error } = await this.supabase
        .from('enhanced_business_profiles')
        .delete()
        .eq('id', profileId)
        .eq('user_id', userId);

      if (error) {
        throw new Error(`Failed to delete enhanced profile: ${error.message}`);
      }

      console.log('✅ [Enhanced Profile Service] Profile deleted successfully');
      return true;

    } catch (error) {
      console.error('❌ [Enhanced Profile Service] Delete error:', error);
      throw error;
    }
  }

  /**
   * Convert legacy profile to enhanced profile
   */
  async convertLegacyProfile(legacyProfile: any, userId: string): Promise<EnhancedBusinessProfile> {
    const enhancedProfile: EnhancedBusinessProfile = {
      coreInfo: {
        businessName: legacyProfile.businessName || '',
        industryCategory: this.mapBusinessTypeToCategory(legacyProfile.businessType),
        businessModel: 'B2C', // Default
        primaryLocation: {
          city: this.extractCity(legacyProfile.location),
          state: '',
          country: this.extractCountry(legacyProfile.location)
        },
        establishmentYear: new Date().getFullYear(),
        businessSize: 'Small (2-10)' // Default
      },
      offerings: this.convertLegacyServices(legacyProfile.services),
      competitiveDifferentiation: {
        // Initialize with empty structure
      },
      trustIndicators: {
        businessLongevity: 1 // Default
      },
      operationalParams: {
        operatingHours: {
          timezone: 'UTC'
        },
        serviceCoverage: {
          geographicBoundaries: [legacyProfile.location || ''],
          deliveryZones: []
        },
        contactChannels: {
          phone: legacyProfile.contactInfo?.phone,
          email: legacyProfile.contactInfo?.email,
          website: legacyProfile.websiteUrl,
          socialMedia: legacyProfile.socialMedia
        }
      },
      contentPreferences: {
        localizationToggle: false,
        contentTone: 'Professional',
        brandVoice: legacyProfile.writingTone || '',
        contentThemes: Array.isArray(legacyProfile.contentThemes)
          ? legacyProfile.contentThemes
          : [legacyProfile.contentThemes || ''],
        visualStyle: legacyProfile.visualStyle || '',
        primaryColor: legacyProfile.primaryColor,
        accentColor: legacyProfile.accentColor,
        backgroundColor: legacyProfile.backgroundColor
      },
      metadata: {
        profileId: `enhanced_${Date.now()}`,
        userId,
        createdAt: new Date(),
        updatedAt: new Date(),
        completionScore: 0,
        isActive: true
      }
    };

    // Calculate completion score
    enhancedProfile.metadata.completionScore = this.calculateCompletionScore(enhancedProfile);

    return enhancedProfile;
  }

  /**
   * Validate profile data
   */
  private validateProfile(profile: EnhancedBusinessProfile): void {
    if (!profile.coreInfo?.businessName) {
      throw new Error('Business name is required');
    }
    if (!profile.coreInfo?.industryCategory) {
      throw new Error('Industry category is required');
    }
    if (!profile.coreInfo?.primaryLocation?.city) {
      throw new Error('Primary location city is required');
    }
    if (!profile.coreInfo?.primaryLocation?.country) {
      throw new Error('Primary location country is required');
    }
  }

  /**
   * Calculate profile completion score
   */
  private calculateCompletionScore(profile: EnhancedBusinessProfile): number {
    let score = 0;
    const maxScore = 100;

    // Core info (30 points)
    if (profile.coreInfo?.businessName) score += 5;
    if (profile.coreInfo?.industryCategory) score += 5;
    if (profile.coreInfo?.businessModel) score += 5;
    if (profile.coreInfo?.primaryLocation?.city) score += 5;
    if (profile.coreInfo?.primaryLocation?.country) score += 5;
    if (profile.coreInfo?.establishmentYear) score += 5;

    // Offerings (25 points)
    if (profile.offerings?.length > 0) score += 15;
    if (profile.offerings?.some(o => o.pricingStructure?.basePrice || o.pricingStructure?.hourlyRate)) score += 10;

    // Trust indicators (20 points)
    if (profile.trustIndicators?.businessLongevity) score += 5;
    if (profile.trustIndicators?.customerBase?.customersServed) score += 5;
    if (profile.trustIndicators?.reviewScores?.google) score += 5;
    if (profile.trustIndicators?.professionalCredentials?.certifications?.length) score += 5;

    // Operational params (15 points)
    if (profile.operationalParams?.contactChannels?.phone) score += 5;
    if (profile.operationalParams?.contactChannels?.email) score += 5;
    if (profile.operationalParams?.operatingHours) score += 5;

    // Content preferences (10 points)
    if (profile.contentPreferences?.brandVoice) score += 5;
    if (profile.contentPreferences?.contentThemes?.length) score += 5;

    return Math.min(maxScore, score);
  }

  /**
   * Helper methods for legacy conversion
   */
  private mapBusinessTypeToCategory(businessType: string): any {
    const mapping: Record<string, any> = {
      'restaurant': 'Restaurant',
      'retail': 'Retail',
      'service': 'Services',
      'healthcare': 'Healthcare',
      'tech': 'Tech',
      'technology': 'Tech',
      'education': 'Education',
      'finance': 'Finance',
      'real estate': 'Real Estate'
    };
    return mapping[businessType?.toLowerCase()] || 'Other';
  }

  private extractCity(location: string): string {
    if (!location) return '';
    const parts = location.split(',');
    return parts[0]?.trim() || '';
  }

  private extractCountry(location: string): string {
    if (!location) return '';
    const parts = location.split(',');
    return parts[parts.length - 1]?.trim() || '';
  }

  private convertLegacyServices(services: any): ProductService[] {
    if (!services) return [];

    const serviceArray = Array.isArray(services) ? services : [services];
    return serviceArray.map((service, index) => ({
      id: `service_${index}`,
      name: typeof service === 'string' ? service : service.name || 'Service',
      description: typeof service === 'string' ? service : service.description || '',
      pricingStructure: {
        type: 'Custom' as const,
        currency: 'USD'
      },
      deliveryTimeline: {
        estimatedTime: 'Contact for details'
      },
      fulfillmentMethod: 'In-person' as const,
      targetCustomerProfile: {
        demographics: [],
        painPoints: []
      },
      primaryBenefits: [],
      isActive: true
    }));
  }
}

// Export singleton instance
export const enhancedBusinessProfileService = new EnhancedBusinessProfileService();
