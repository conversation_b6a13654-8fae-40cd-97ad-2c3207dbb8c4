'use client';

/**
 * Enhanced Profile Manager
 * Manages multiple enhanced business profiles with switching capabilities
 */

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  MoreVertical, 
  Plus, 
  Edit, 
  Trash2, 
  CheckCircle, 
  Globe, 
  Building, 
  MapPin,
  Calendar,
  Users,
  Star,
  AlertTriangle
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useToast } from '@/hooks/use-toast';
import { useEnhancedBrand } from '@/contexts/enhanced-brand-context';
import { EnhancedProfileForm } from '@/components/enhanced-business-profile/enhanced-profile-form';
import type { EnhancedBusinessProfile } from '@/lib/types/enhanced-business-profile';

interface EnhancedProfileManagerProps {
  onProfileSelect?: (profile: EnhancedBusinessProfile) => void;
  showCreateButton?: boolean;
}

export function EnhancedProfileManager({ 
  onProfileSelect, 
  showCreateButton = true 
}: EnhancedProfileManagerProps) {
  const { toast } = useToast();
  const { 
    enhancedProfiles, 
    currentEnhancedProfile, 
    selectEnhancedProfile,
    saveEnhancedProfile,
    updateEnhancedProfile,
    deleteEnhancedProfile,
    getProfileCompletionScore,
    loading,
    saving
  } = useEnhancedBrand();

  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingProfile, setEditingProfile] = useState<EnhancedBusinessProfile | null>(null);
  const [deletingProfileId, setDeletingProfileId] = useState<string | null>(null);

  const handleCreateProfile = () => {
    setEditingProfile(null);
    setShowCreateForm(true);
  };

  const handleEditProfile = (profile: EnhancedBusinessProfile) => {
    setEditingProfile(profile);
    setShowCreateForm(true);
  };

  const handleDeleteProfile = async (profile: EnhancedBusinessProfile) => {
    if (!confirm(`Are you sure you want to delete "${profile.coreInfo.businessName}"? This action cannot be undone.`)) {
      return;
    }

    setDeletingProfileId(profile.metadata.profileId);

    try {
      await deleteEnhancedProfile(profile.metadata.profileId);
      
      toast({
        title: "Profile Deleted",
        description: `${profile.coreInfo.businessName} has been deleted successfully.`
      });
      
    } catch (error) {
      console.error('Error deleting profile:', error);
      toast({
        title: "Delete Failed",
        description: error instanceof Error ? error.message : "Failed to delete profile. Please try again.",
        variant: "destructive"
      });
    } finally {
      setDeletingProfileId(null);
    }
  };

  const handleSelectProfile = (profile: EnhancedBusinessProfile) => {
    selectEnhancedProfile(profile);
    onProfileSelect?.(profile);
    
    toast({
      title: "Profile Selected",
      description: `Switched to ${profile.coreInfo.businessName}`
    });
  };

  const handleSaveProfile = async (profile: EnhancedBusinessProfile) => {
    try {
      if (editingProfile) {
        // Update existing profile
        await updateEnhancedProfile(editingProfile.metadata.profileId, profile);
        toast({
          title: "Profile Updated",
          description: `${profile.coreInfo.businessName} has been updated successfully.`
        });
      } else {
        // Create new profile
        await saveEnhancedProfile(profile);
        toast({
          title: "Profile Created",
          description: `${profile.coreInfo.businessName} has been created successfully.`
        });
      }
      
      setShowCreateForm(false);
      setEditingProfile(null);
      
    } catch (error) {
      console.error('Error saving profile:', error);
      // Error handling is done in the context
    }
  };

  const handleCancelForm = () => {
    setShowCreateForm(false);
    setEditingProfile(null);
  };

  const getCompletionBadgeColor = (score: number) => {
    if (score >= 80) return 'bg-green-500';
    if (score >= 60) return 'bg-blue-500';
    if (score >= 40) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const getProfileStatusIcon = (profile: EnhancedBusinessProfile) => {
    const score = getProfileCompletionScore(profile);
    if (score >= 80) return <CheckCircle className="h-4 w-4 text-green-500" />;
    if (score >= 60) return <Star className="h-4 w-4 text-blue-500" />;
    return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
  };

  if (showCreateForm) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold">
            {editingProfile ? 'Edit Profile' : 'Create Enhanced Profile'}
          </h2>
          <Button variant="outline" onClick={handleCancelForm}>
            Back to Profiles
          </Button>
        </div>
        
        <EnhancedProfileForm
          initialProfile={editingProfile || undefined}
          onSave={handleSaveProfile}
          onCancel={handleCancelForm}
          isLoading={saving}
        />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Enhanced Business Profiles</h2>
          <p className="text-muted-foreground">
            Manage your comprehensive business profiles for advanced content generation
          </p>
        </div>
        {showCreateButton && (
          <Button onClick={handleCreateProfile} className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            Create Profile
          </Button>
        )}
      </div>

      {/* Profiles Grid */}
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="pt-6">
                <div className="space-y-3">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  <div className="h-2 bg-gray-200 rounded w-full"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : enhancedProfiles.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <Building className="h-12 w-12 text-muted-foreground mx-auto" />
              <div>
                <h3 className="text-lg font-semibold">No Enhanced Profiles Yet</h3>
                <p className="text-muted-foreground">
                  Create your first enhanced business profile to unlock advanced content generation features.
                </p>
              </div>
              {showCreateButton && (
                <Button onClick={handleCreateProfile} className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  Create Your First Profile
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {enhancedProfiles.map((profile) => {
            const completionScore = getProfileCompletionScore(profile);
            const isSelected = currentEnhancedProfile?.metadata.profileId === profile.metadata.profileId;
            const isDeleting = deletingProfileId === profile.metadata.profileId;

            return (
              <Card 
                key={profile.metadata.profileId} 
                className={`cursor-pointer transition-all hover:shadow-md ${
                  isSelected ? 'ring-2 ring-blue-500 bg-blue-50' : ''
                } ${isDeleting ? 'opacity-50' : ''}`}
                onClick={() => !isDeleting && handleSelectProfile(profile)}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <CardTitle className="text-lg truncate">
                        {profile.coreInfo.businessName}
                      </CardTitle>
                      <CardDescription className="flex items-center gap-1">
                        <Building className="h-3 w-3" />
                        {profile.coreInfo.industryCategory}
                      </CardDescription>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      {getProfileStatusIcon(profile)}
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            className="h-8 w-8 p-0"
                            onClick={(e) => e.stopPropagation()}
                            disabled={isDeleting}
                          >
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={(e) => {
                            e.stopPropagation();
                            handleSelectProfile(profile);
                          }}>
                            <CheckCircle className="h-4 w-4 mr-2" />
                            Select Profile
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={(e) => {
                            e.stopPropagation();
                            handleEditProfile(profile);
                          }}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit Profile
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteProfile(profile);
                            }}
                            className="text-red-600"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete Profile
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="space-y-4">
                  {/* Completion Score */}
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium">Completion</span>
                      <Badge className={getCompletionBadgeColor(completionScore)}>
                        {completionScore}%
                      </Badge>
                    </div>
                    <Progress value={completionScore} className="h-2" />
                  </div>

                  {/* Profile Details */}
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <MapPin className="h-3 w-3" />
                      <span className="truncate">
                        {profile.coreInfo.primaryLocation.city}, {profile.coreInfo.primaryLocation.country}
                      </span>
                    </div>
                    
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <Calendar className="h-3 w-3" />
                      <span>Est. {profile.coreInfo.establishmentYear}</span>
                    </div>

                    {profile.offerings && profile.offerings.length > 0 && (
                      <div className="flex items-center gap-2 text-muted-foreground">
                        <Users className="h-3 w-3" />
                        <span>{profile.offerings.length} offerings</span>
                      </div>
                    )}
                  </div>

                  {/* Features */}
                  <div className="flex flex-wrap gap-1">
                    {profile.contentPreferences.localizationToggle && (
                      <Badge variant="secondary" className="text-xs flex items-center gap-1">
                        <Globe className="h-2 w-2" />
                        Localized
                      </Badge>
                    )}
                    {profile.coreInfo.businessModel && (
                      <Badge variant="outline" className="text-xs">
                        {profile.coreInfo.businessModel}
                      </Badge>
                    )}
                    {isSelected && (
                      <Badge className="text-xs bg-blue-500">
                        Active
                      </Badge>
                    )}
                  </div>

                  {/* Last Updated */}
                  <div className="text-xs text-muted-foreground">
                    Updated {new Date(profile.metadata.updatedAt).toLocaleDateString()}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}

      {/* Current Selection Alert */}
      {currentEnhancedProfile && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>
            <strong>Active Profile:</strong> {currentEnhancedProfile.coreInfo.businessName} - 
            This profile will be used for content generation and other features.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}
