/**
 * Comprehensive Business Profile Analyzer
 * Implements the Universal AI System Prompt for business-specific content generation
 */

import { BrandProfile } from '@/lib/types';

export interface ComprehensiveBusinessProfile {
  // Basic Company Profile
  companyName: string;
  industry: string;
  businessType: 'B2B' | 'B2C' | 'Both';
  location: string;
  founded?: string;

  // Products/Services
  products: Array<{
    name: string;
    description: string;
    pricing?: string;
    delivery?: string;
    targetCustomer: string;
    keyBenefits: string[];
  }>;

  // Competitive Advantages
  advantages: {
    speed?: string;
    quality?: string;
    price?: string;
    convenience?: string;
    expertise?: string;
    customerService?: string;
  };

  // Credentials & Social Proof
  credentials: {
    yearsInBusiness?: number;
    customerCount?: string;
    certifications?: string[];
    reviews?: string;
    awards?: string[];
    partnerships?: string[];
  };

  // Business Operations
  operations: {
    hours?: string;
    serviceArea?: string;
    capacity?: string;
    guarantees?: string[];
    contactMethods?: string[];
  };

  // Content Strategy
  contentStrategy: {
    tone: 'professional' | 'casual' | 'friendly' | 'authoritative';
    targetAudience: string;
    keyMessages: string[];
    callToActions: string[];
    localContext: string;
    // Enhanced fields
    brandPersonality: string;
    preferredLanguages: string[];
  };
}

export class ComprehensiveBusinessAnalyzer {
  private static instance: ComprehensiveBusinessAnalyzer;

  public static getInstance(): ComprehensiveBusinessAnalyzer {
    if (!ComprehensiveBusinessAnalyzer.instance) {
      ComprehensiveBusinessAnalyzer.instance = new ComprehensiveBusinessAnalyzer();
    }
    return ComprehensiveBusinessAnalyzer.instance;
  }

  async analyzeComprehensiveProfile(brandProfile: BrandProfile): Promise<ComprehensiveBusinessProfile> {
    const {
      businessName = '',
      businessType = '',
      location = '',
      description = '',
      services = '',
      keyFeatures = '',
      competitiveAdvantages = '',
      targetAudience = '',
      websiteUrl = '',
      // Enhanced profile fields
      brandPersonality = '',
      uniqueSellingPoints = '',
      establishmentYear = '',
      businessSize = '',
      priceRange = '',
      preferredLanguages = ''
    } = brandProfile as any;

    // Parse services and features
    const servicesList = this.parseTextToList(services);
    const featuresList = this.parseTextToList(keyFeatures);
    const advantagesList = this.parseTextToList(competitiveAdvantages);

    // Parse enhanced profile fields
    const uniqueSellingPointsList = this.parseTextToList(uniqueSellingPoints);
    const languagesList = this.parseTextToList(preferredLanguages);

    // Extract industry from business type
    const industry = this.extractIndustry(businessType);

    // Determine B2B/B2C/Both
    const businessModel = this.determineBusinessModel(businessType, targetAudience);

    // Use enhanced establishment year or extract from description
    const founded = establishmentYear || this.extractFoundedYear(description);

    // Analyze products/services with enhanced data
    const products = this.analyzeProducts(servicesList, featuresList, businessType, uniqueSellingPointsList);

    // Extract competitive advantages with enhanced unique selling points
    const advantages = this.extractCompetitiveAdvantages(advantagesList, description, businessType, uniqueSellingPointsList);

    // Extract credentials and social proof with enhanced business size
    const credentials = this.extractCredentials(description, businessName, location, businessSize, establishmentYear);

    // Extract business operations with enhanced data
    const operations = this.extractOperations(description, location, websiteUrl, priceRange);

    // Generate content strategy with enhanced personality and languages
    const contentStrategy = this.generateContentStrategy(
      businessType,
      targetAudience,
      location,
      industry,
      products,
      advantages,
      brandPersonality,
      languagesList
    );

    return {
      companyName: businessName,
      industry,
      businessType: businessModel,
      location,
      founded,
      products,
      advantages,
      credentials,
      operations,
      contentStrategy
    };
  }

  private parseTextToList(text: string): string[] {
    if (!text) return [];
    return text.split('\n')
      .map(item => item.trim())
      .filter(item => item.length > 0);
  }

  private extractIndustry(businessType: string): string {
    const type = businessType.toLowerCase();

    if (type.includes('finance') || type.includes('banking') || type.includes('fintech')) {
      return 'Financial Services';
    }
    if (type.includes('food') || type.includes('restaurant') || type.includes('cafe')) {
      return 'Food & Beverage';
    }
    if (type.includes('tech') || type.includes('software') || type.includes('electronics')) {
      return 'Technology';
    }
    if (type.includes('health') || type.includes('medical') || type.includes('wellness')) {
      return 'Healthcare';
    }
    if (type.includes('retail') || type.includes('shop') || type.includes('store')) {
      return 'Retail';
    }
    if (type.includes('service') || type.includes('consulting')) {
      return 'Professional Services';
    }

    return 'General Business';
  }

  private determineBusinessModel(businessType: string, targetAudience: string): 'B2B' | 'B2C' | 'Both' {
    const type = businessType.toLowerCase();
    const audience = targetAudience.toLowerCase();

    if (type.includes('b2b') || audience.includes('business') || audience.includes('company')) {
      return 'B2B';
    }
    if (type.includes('b2c') || audience.includes('consumer') || audience.includes('individual')) {
      return 'B2C';
    }

    // Default based on industry
    if (type.includes('finance') || type.includes('consulting')) {
      return 'Both';
    }

    return 'B2C';
  }

  private extractFoundedYear(description: string): string | undefined {
    const yearMatch = description.match(/(?:founded|established|since|started)\s+(?:in\s+)?(\d{4})/i);
    return yearMatch ? yearMatch[1] : undefined;
  }

  private analyzeProducts(services: string[], features: string[], businessType: string, uniqueSellingPoints: string[] = []) {
    const products = [];

    // Convert services to products
    services.forEach(service => {
      if (service.trim()) {
        products.push({
          name: service,
          description: this.generateServiceDescription(service, businessType),
          pricing: this.extractPricing(service),
          delivery: this.extractDelivery(service),
          targetCustomer: this.identifyTargetCustomer(service, businessType),
          keyBenefits: this.extractServiceBenefits(service, businessType)
        });
      }
    });

    // If no services, create from business type
    if (products.length === 0) {
      products.push({
        name: `${businessType} Services`,
        description: `Professional ${businessType.toLowerCase()} solutions`,
        targetCustomer: 'Local customers',
        keyBenefits: ['Quality service', 'Professional approach', 'Local expertise']
      });
    }

    return products;
  }

  private generateServiceDescription(service: string, businessType: string): string {
    const serviceLower = service.toLowerCase();

    if (serviceLower.includes('banking')) {
      return 'Secure and reliable banking services with competitive rates and instant transfers';
    }
    if (serviceLower.includes('payment')) {
      return 'Fast, secure payment processing with zero hidden fees';
    }
    if (serviceLower.includes('loan')) {
      return 'Quick loan approval with flexible repayment terms and competitive interest rates';
    }
    if (serviceLower.includes('cookie') || serviceLower.includes('food')) {
      return 'Fresh, delicious baked goods made with premium ingredients and traditional recipes';
    }
    if (serviceLower.includes('electronic') || serviceLower.includes('tech')) {
      return 'Latest technology products with expert technical support and warranty coverage';
    }

    return `Professional ${service.toLowerCase()} services tailored to your needs`;
  }

  private extractPricing(service: string): string | undefined {
    const pricingMatch = service.match(/(?:from\s+)?\$?(\d+(?:\.\d{2})?)(?:\s*(?:per|\/)\s*(?:month|year|hour|day|unit))?/i);
    return pricingMatch ? `Starting from $${pricingMatch[1]}` : undefined;
  }

  private extractDelivery(service: string): string | undefined {
    const serviceLower = service.toLowerCase();

    if (serviceLower.includes('instant') || serviceLower.includes('immediate')) {
      return 'Instant delivery';
    }
    if (serviceLower.includes('24') || serviceLower.includes('same day')) {
      return 'Same day delivery';
    }
    if (serviceLower.includes('fast') || serviceLower.includes('quick')) {
      return 'Fast delivery';
    }

    return undefined;
  }

  private identifyTargetCustomer(service: string, businessType: string): string {
    const serviceLower = service.toLowerCase();

    if (serviceLower.includes('business') || serviceLower.includes('corporate')) {
      return 'Businesses and corporations';
    }
    if (serviceLower.includes('individual') || serviceLower.includes('personal')) {
      return 'Individual customers';
    }
    if (serviceLower.includes('family')) {
      return 'Families';
    }

    return 'Local customers';
  }

  private extractServiceBenefits(service: string, businessType: string): string[] {
    const benefits = [];
    const serviceLower = service.toLowerCase();

    if (serviceLower.includes('secure') || serviceLower.includes('safe')) {
      benefits.push('Secure and safe transactions');
    }
    if (serviceLower.includes('fast') || serviceLower.includes('quick')) {
      benefits.push('Fast and efficient service');
    }
    if (serviceLower.includes('reliable') || serviceLower.includes('trusted')) {
      benefits.push('Reliable and trustworthy');
    }
    if (serviceLower.includes('local') || serviceLower.includes('kenya')) {
      benefits.push('Local expertise and support');
    }
    if (serviceLower.includes('professional')) {
      benefits.push('Professional service quality');
    }

    return benefits.length > 0 ? benefits : ['Quality service', 'Customer satisfaction'];
  }

  private extractCompetitiveAdvantages(advantages: string[], description: string, businessType: string, uniqueSellingPoints: string[] = []) {
    const extracted = {
      speed: undefined as string | undefined,
      quality: undefined as string | undefined,
      price: undefined as string | undefined,
      convenience: undefined as string | undefined,
      expertise: undefined as string | undefined,
      customerService: undefined as string | undefined
    };

    const allText = [...advantages, description].join(' ').toLowerCase();

    // Extract speed advantages
    if (allText.includes('fast') || allText.includes('quick') || allText.includes('instant')) {
      extracted.speed = 'Fast service delivery and quick response times';
    }

    // Extract quality advantages
    if (allText.includes('quality') || allText.includes('premium') || allText.includes('expert')) {
      extracted.quality = 'High-quality products and professional service';
    }

    // Extract price advantages
    if (allText.includes('affordable') || allText.includes('competitive') || allText.includes('low cost')) {
      extracted.price = 'Competitive pricing and great value';
    }

    // Extract convenience advantages
    if (allText.includes('convenient') || allText.includes('easy') || allText.includes('accessible')) {
      extracted.convenience = 'Convenient location and easy access';
    }

    // Extract expertise advantages
    if (allText.includes('experience') || allText.includes('expert') || allText.includes('years')) {
      extracted.expertise = 'Years of experience and specialized knowledge';
    }

    // Extract customer service advantages
    if (allText.includes('support') || allText.includes('service') || allText.includes('help')) {
      extracted.customerService = 'Excellent customer support and service';
    }

    return extracted;
  }

  private extractCredentials(description: string, businessName: string, location: string, businessSize: string = '', establishmentYear: string = '') {
    const credentials = {
      yearsInBusiness: undefined as number | undefined,
      customerCount: undefined as string | undefined,
      certifications: [] as string[],
      reviews: undefined as string | undefined,
      awards: [] as string[],
      partnerships: [] as string[]
    };

    const text = description.toLowerCase();

    // Extract years in business
    const yearsMatch = text.match(/(\d+)\s*(?:years?|yrs?)\s*(?:in\s*business|experience|serving)/i);
    if (yearsMatch) {
      credentials.yearsInBusiness = parseInt(yearsMatch[1]);
    }

    // Extract customer count
    const customerMatch = text.match(/(\d+(?:,\d+)*(?:k|thousand|million)?)\s*(?:customers?|clients?|served)/i);
    if (customerMatch) {
      credentials.customerCount = customerMatch[1];
    }

    // Extract certifications
    if (text.includes('certified') || text.includes('licensed')) {
      credentials.certifications.push('Licensed and certified');
    }

    // Extract reviews
    const reviewMatch = text.match(/(\d+(?:\.\d+)?)\s*(?:star|★)/i);
    if (reviewMatch) {
      credentials.reviews = `${reviewMatch[1]} star rating`;
    }

    return credentials;
  }

  private extractOperations(description: string, location: string, websiteUrl: string, priceRange: string = '') {
    const operations = {
      hours: undefined as string | undefined,
      serviceArea: undefined as string | undefined,
      capacity: undefined as string | undefined,
      guarantees: [] as string[],
      contactMethods: [] as string[]
    };

    const text = description.toLowerCase();

    // Extract hours
    const hoursMatch = text.match(/(?:open|hours?)\s*:?\s*([^.!?]+)/i);
    if (hoursMatch) {
      operations.hours = hoursMatch[1].trim();
    }

    // Set service area
    if (location.toLowerCase().includes('kenya')) {
      operations.serviceArea = 'Kenya and surrounding areas';
    } else {
      operations.serviceArea = location || 'Local area';
    }

    // Extract guarantees
    if (text.includes('guarantee') || text.includes('warranty')) {
      operations.guarantees.push('Money-back guarantee');
    }
    if (text.includes('satisfaction')) {
      operations.guarantees.push('Customer satisfaction guarantee');
    }

    // Add contact methods
    if (websiteUrl) {
      operations.contactMethods.push('Website');
    }
    operations.contactMethods.push('Phone', 'Email');

    return operations;
  }

  private generateContentStrategy(
    businessType: string,
    targetAudience: string,
    location: string,
    industry: string,
    products: any[],
    advantages: any,
    brandPersonality: string = '',
    preferredLanguages: string[] = []
  ) {
    // Determine tone based on brand personality or industry
    let tone: 'professional' | 'casual' | 'friendly' | 'authoritative' = 'professional';

    // Use brand personality if provided
    if (brandPersonality) {
      const personalityLower = brandPersonality.toLowerCase();
      if (personalityLower.includes('friendly') || personalityLower.includes('warm')) {
        tone = 'friendly';
      } else if (personalityLower.includes('casual') || personalityLower.includes('relaxed')) {
        tone = 'casual';
      } else if (personalityLower.includes('authoritative') || personalityLower.includes('expert')) {
        tone = 'authoritative';
      }
    } else {
      // Fallback to industry-based tone
      if (industry === 'Food & Beverage') {
        tone = 'friendly';
      } else if (industry === 'Technology') {
        tone = 'professional';
      } else if (industry === 'Healthcare') {
        tone = 'authoritative';
      }
    }

    // Generate key messages
    const keyMessages = [
      `Quality ${businessType.toLowerCase()} services in ${location}`,
      `Trusted by local customers`,
      `Professional and reliable service`
    ];

    // Add product-specific messages
    products.forEach(product => {
      if (product.keyBenefits.length > 0) {
        keyMessages.push(product.keyBenefits[0]);
      }
    });

    // Generate CTAs
    const callToActions = [
      `Contact ${businessType} today`,
      `Get started now`,
      `Learn more about our services`
    ];

    // Enhanced local context with language preferences
    let localContext = `Serving ${location} with quality ${businessType.toLowerCase()} services`;
    if (preferredLanguages.length > 0) {
      localContext += ` | Languages: ${preferredLanguages.join(', ')}`;
    }

    return {
      tone,
      targetAudience: targetAudience || 'Local customers',
      keyMessages: keyMessages.slice(0, 5),
      callToActions,
      localContext,
      // Enhanced fields
      brandPersonality: brandPersonality || 'Professional and reliable',
      preferredLanguages: preferredLanguages
    };
  }
}

