/**
 * Enhanced Creativity Prompts - Advanced AI prompting for dynamic content generation
 * Implements stronger creativity requirements and anti-repetition measures
 */

export interface CreativityConfig {
  businessName: string;
  businessType: string;
  location: string;
  generationId: number;
  approach: string;
  creativityBoost: number;
  businessDetails: any;
  trendingData?: any;
  businessIntelligence?: any;
  wordPool?: any;
}

export class EnhancedCreativityPrompts {
  
  /**
   * Generate enhanced creativity prompt with strong anti-repetition measures
   */
  static generateCreativityPrompt(config: CreativityConfig): string {
    const {
      businessName,
      businessType,
      location,
      generationId,
      approach,
      creativityBoost,
      businessDetails,
      trendingData,
      businessIntelligence
    } = config;

    return `
🎯 ADVANCED CREATIVITY ENGINE - GENERATION ${generationId}
You are an ELITE creative director and copywriter with 15+ years of experience creating viral, conversion-focused content. Your mission: Create content so unique and compelling that it becomes the benchmark for ${businessType} marketing.

🚀 MANDATORY CREATIVITY REQUIREMENTS:

1. **UNIQUENESS MANDATE**:
   - This content must be 100% original and specific to ${businessName}
   - NO generic phrases that could apply to any ${businessType}
   - NO template-like language patterns
   - Every word must add unique value specific to this business
   - Content must be so specific that it could NEVER be used for another business

2. **APPROACH ENFORCEMENT**: ${approach}
   You MUST use this specific creative approach - no exceptions:
   ${this.getApproachInstructions(approach, businessName, location, creativityBoost)}

3. **CREATIVITY BOOST CHALLENGE**: ${creativityBoost}
   Create content that demonstrates creative mastery level ${creativityBoost}:
   - Use unexpected word combinations
   - Employ creative metaphors and analogies
   - Create memorable phrases that stick
   - Use psychological triggers effectively
   - Build emotional connections

4. **STRICT ANTI-REPETITION PROTOCOL**:
   ❌ FORBIDDEN PATTERNS (NEVER USE):
   - "2025's Best-Kept Secret" or any year references
   - "best-kept secret", "secret", "hidden gem", "mystery"
   - "chakula kizuri" (use "chakula bora", "vyakula vizuri", "lishe nzuri" instead)
   - "Shop now via the link in our bio! Karibu!"
   - "Discover", "Experience", "Taste the", "Try our", "Indulge in"
   - "for your familia's delight" or similar family references
   - "Tired of..." opening patterns
   - "Experience [Location]'s authentic [BusinessType] revolution"
   - Any phrase that sounds like a template

   ✅ CREATIVITY REQUIREMENTS:
   - Use fresh, unexpected vocabulary
   - Create original metaphors and analogies
   - Employ creative sentence structures
   - Use psychological triggers strategically
   - Build emotional resonance
   - Create memorable, quotable phrases

5. **BUSINESS-SPECIFIC INTELLIGENCE**:
   - Headlines: Must reference specific business features, current events, or trending topics
   - Subheadlines: Must mention actual services, products, or unique selling points
   - Captions: Must tell a story specific to this business and location
   - CTAs: Must be action-oriented and specific to the business offering

6. **DATA INTEGRATION MANDATE**:
   ${trendingData ? `- RSS Trends: ${trendingData.keywords?.slice(0, 5).join(', ')}` : ''}
   ${businessIntelligence ? `- Business Intelligence: ${JSON.stringify(businessIntelligence).slice(0, 200)}...` : ''}
   ${businessDetails.services ? `- Services: ${businessDetails.services}` : ''}
   ${businessDetails.expertise ? `- Expertise: ${businessDetails.expertise}` : ''}
   ${wordPool ? `- Fresh Word Pool: Use these creative words: ${this.getWordPoolSuggestions(wordPool)}` : ''}

   ALL content must weave together this data into compelling, cohesive marketing copy.

7. **PSYCHOLOGICAL TRIGGERS** (Use strategically):
   - Scarcity: Limited time, limited quantity, exclusive access
   - Social Proof: Customer testimonials, popularity indicators, community validation
   - Authority: Expert insights, industry knowledge, proven results
   - Reciprocity: Valuable tips, free insights, helpful information
   - Commitment: Personal investment, public declaration, identity alignment
   - Liking: Similarity, compliments, cooperation, association
   - Consistency: Align with existing beliefs, past commitments

8. **EMOTIONAL RESONANCE REQUIREMENTS**:
   - Create content that makes people FEEL something
   - Use emotional triggers: joy, surprise, anticipation, belonging, achievement
   - Build emotional connections with the target audience
   - Make the content memorable and shareable

9. **CONVERSION PSYCHOLOGY**:
   - Address specific pain points
   - Present clear solutions
   - Create urgency without being pushy
   - Offer clear value propositions
   - Make the next step obvious and easy

10. **CONTENT COHESION MANDATE**:
    - ALL components must tell the SAME story
    - Headlines, subheadlines, captions, and CTAs must work together
    - No contradictory information between components
    - Each component reinforces the core message
    - Create a unified campaign, not separate pieces

🎨 CREATIVE EXCELLENCE STANDARDS:
- Every sentence must earn its place
- Use active voice and strong verbs
- Create visual imagery with words
- Use sensory language when appropriate
- Make it scannable and engaging
- Balance creativity with clarity
- Ensure accessibility and inclusivity

🚀 GENERATION UNIQUENESS ID: ${generationId}
Use this ID to ensure this content is completely different from any previous generation.
This content must be so unique that it becomes a case study in creative marketing excellence.

CREATE CONTENT THAT MAKES PEOPLE STOP, READ, AND ACT.
`;
  }

  /**
   * Get approach-specific instructions
   */
  private static getApproachInstructions(approach: string, businessName: string, location: string, creativityBoost: number): string {
    const approachMap: Record<string, string> = {
      'DIRECT_BENEFIT': `
        🎯 DIRECT BENEFIT APPROACH:
        - Lead with the most compelling benefit for customers
        - Use specific, measurable outcomes
        - Focus on "what's in it for them"
        - Use benefit-driven language: "Get", "Achieve", "Gain", "Save"
        - Make benefits tangible and immediate
        - Example angle: "Transform your [specific outcome] in [timeframe]"
      `,
      'SOCIAL_PROOF': `
        👥 SOCIAL PROOF APPROACH:
        - Lead with customer success stories or testimonials
        - Use numbers and statistics to build credibility
        - Highlight community validation and popularity
        - Use social proof language: "Join", "Thousands of", "Proven", "Trusted"
        - Make people feel part of something bigger
        - Example angle: "Join [X] customers who [achieved specific result]"
      `,
      'PROBLEM_SOLUTION': `
        🔧 PROBLEM-SOLUTION APPROACH:
        - Identify a specific pain point your audience faces
        - Present your business as the clear solution
        - Use problem-solution language: "Struggling with", "Finally", "No more", "Solved"
        - Make the problem relatable and urgent
        - Position your solution as the obvious choice
        - Example angle: "Tired of [specific problem]? [BusinessName] has the answer"
      `,
      'LOCAL_INSIDER': `
        🏘️ LOCAL INSIDER APPROACH:
        - Use local knowledge and insider tips
        - Reference local events, trends, or happenings
        - Create "insider" feeling with local expertise
        - Use local language: "Locals know", "Insider tip", "Local favorite"
        - Make people feel like they're getting special local knowledge
        - Example angle: "What locals know about [businessType] in [location]"
      `,
      'URGENCY_SCARCITY': `
        ⏰ URGENCY-SCARCITY APPROACH:
        - Create time-sensitive or quantity-limited offers
        - Use urgency language: "Limited time", "Only", "Hurry", "Last chance"
        - Make people feel they might miss out
        - Use scarcity indicators: "Few spots left", "Selling out", "Almost gone"
        - Create FOMO (Fear of Missing Out)
        - Example angle: "Only [X] spots left for [specific offer]"
      `,
      'QUESTION_HOOK': `
        ❓ QUESTION HOOK APPROACH:
        - Start with a compelling question that resonates
        - Use questions that make people think or feel
        - Create curiosity and engagement
        - Use question language: "What if", "Ever wondered", "Ready to"
        - Make questions specific to your audience's situation
        - Example angle: "What if you could [achieve specific outcome] in [timeframe]?"
      `,
      'STATISTIC_LEAD': `
        📊 STATISTIC LEAD APPROACH:
        - Lead with impressive numbers or statistics
        - Use data to build credibility and interest
        - Make statistics relevant to your audience
        - Use number language: "X% of", "In just X days", "Over X customers"
        - Make numbers meaningful and impactful
        - Example angle: "[X]% of customers see results in [timeframe]"
      `,
      'STORY_ANGLE': `
        📖 STORY ANGLE APPROACH:
        - Tell a compelling story about your business or customers
        - Use narrative elements: character, conflict, resolution
        - Create emotional connection through storytelling
        - Use story language: "Meet", "Last week", "The moment", "Here's what happened"
        - Make stories relatable and memorable
        - Example angle: "Last week, [customer] discovered [specific benefit]"
      `,
      'COMPARISON': `
        ⚖️ COMPARISON APPROACH:
        - Compare your business to alternatives or competitors
        - Highlight what makes you different and better
        - Use comparison language: "Unlike", "While others", "The difference is"
        - Make comparisons specific and meaningful
        - Position your business as the superior choice
        - Example angle: "While others [generic approach], we [specific advantage]"
      `,
      'NEWS_TREND': `
        📰 NEWS-TREND APPROACH:
        - Connect to current events, trends, or seasonal opportunities
        - Use trending topics to make content timely and relevant
        - Use trend language: "Trending", "Hot topic", "Everyone's talking about"
        - Make connections between trends and your business
        - Create timely, relevant content
        - Example angle: "As [trending topic] continues, [businessName] offers [relevant solution]"
      `
    };

    return approachMap[approach] || `
      🎯 CREATIVE APPROACH:
      - Use your creativity to make this content stand out
      - Focus on what makes ${businessName} unique
      - Create compelling, memorable content
      - Use psychological triggers effectively
      - Make it specific to this business and location
    `;
  }

  /**
   * Generate enhanced anti-repetition rules
   */
  static generateAntiRepetitionRules(businessType: string, location: string): string {
    return `
🚫 ENHANCED ANTI-REPETITION PROTOCOL:

FORBIDDEN PHRASES (NEVER USE):
- "2025's Best-Kept Secret" or any year references
- "best-kept secret", "secret", "hidden gem", "mystery"
- "chakula kizuri" (use "chakula bora", "vyakula vizuri", "lishe nzuri" instead)
- "Shop now via the link in our bio! Karibu!"
- "Discover", "Experience", "Taste the", "Try our", "Indulge in"
- "for your familia's delight" or similar family references
- "Tired of..." opening patterns
- "Experience [Location]'s authentic [BusinessType] revolution"
- "Get instant access now!" or similar generic CTAs
- "[Location]'s most [adjective] [BusinessType] experience"
- "where [location] locals [action] [adjective] [businesstype]"
- Any phrase that sounds like a template

REPETITIVE PATTERN DETECTION:
- Check for overused opening words
- Avoid formulaic sentence structures
- Prevent template-like language patterns
- Ensure each generation is genuinely unique
- Use creative alternatives to common phrases

CREATIVITY ENFORCEMENT:
- Use unexpected word combinations
- Employ creative metaphors and analogies
- Create memorable, quotable phrases
- Use psychological triggers strategically
- Build emotional resonance
- Make content specific to ${businessName}

UNIQUENESS VERIFICATION:
- Content must be specific to ${businessName}
- No generic phrases that could apply to any ${businessType}
- Every sentence must add unique value
- Content must be so specific it could never be used for another business
- Use actual business data and trending information
`;
  }

  /**
   * Generate dynamic word pool for creative variation
   */
  static generateDynamicWordPool(businessType: string, creativityBoost: number): string[] {
    const baseWords = [
      'transform', 'revolutionize', 'elevate', 'amplify', 'supercharge',
      'unleash', 'ignite', 'spark', 'fuel', 'power', 'boost', 'accelerate',
      'breakthrough', 'innovation', 'excellence', 'mastery', 'precision',
      'authentic', 'genuine', 'real', 'true', 'pure', 'premium', 'elite',
      'exclusive', 'unique', 'special', 'rare', 'limited', 'exceptional',
      'outstanding', 'remarkable', 'extraordinary', 'incredible', 'amazing',
      'stunning', 'breathtaking', 'mesmerizing', 'captivating', 'compelling'
    ];

    const businessSpecificWords = {
      'restaurant': ['savor', 'indulge', 'feast', 'delight', 'culinary', 'gourmet', 'artisanal'],
      'bakery': ['fresh', 'artisan', 'handcrafted', 'delicious', 'mouthwatering', 'delectable'],
      'fitness': ['transform', 'achieve', 'conquer', 'dominate', 'excel', 'thrive', 'succeed'],
      'beauty': ['glow', 'radiant', 'flawless', 'stunning', 'gorgeous', 'beautiful', 'elegant'],
      'retail': ['discover', 'explore', 'find', 'uncover', 'reveal', 'showcase', 'feature'],
      'tech': ['innovate', 'advance', 'evolve', 'upgrade', 'enhance', 'optimize', 'streamline'],
      'service': ['expert', 'professional', 'skilled', 'experienced', 'qualified', 'certified']
    };

    const typeWords = businessSpecificWords[businessType.toLowerCase()] || businessSpecificWords['service'];
    const allWords = [...baseWords, ...typeWords];
    
    // Shuffle and return based on creativity boost
    const shuffled = allWords.sort(() => Math.random() - 0.5);
    return shuffled.slice(0, 15 + (creativityBoost % 10));
  }

  /**
   * Generate creative framework instructions
   */
  static generateCreativeFramework(approach: string, businessName: string): string {
    const frameworks = {
      'AIDA': `
        🎯 AIDA FRAMEWORK:
        - ATTENTION: Hook with compelling opening
        - INTEREST: Build curiosity and engagement
        - DESIRE: Create want and need
        - ACTION: Clear call-to-action
      `,
      'PAS': `
        🎯 PAS FRAMEWORK:
        - PROBLEM: Identify specific pain point
        - AGITATE: Amplify the problem's impact
        - SOLUTION: Present your business as the answer
      `,
      'BEFORE_AFTER_BRIDGE': `
        🎯 BEFORE-AFTER-BRIDGE:
        - BEFORE: Current frustrating situation
        - AFTER: Desired outcome/result
        - BRIDGE: How your business gets them there
      `,
      'STORYTELLING': `
        🎯 STORYTELLING:
        - CHARACTER: Relatable person/situation
        - CONFLICT: Challenge or problem faced
        - RESOLUTION: How your business solved it
        - LESSON: Value for the audience
      `,
      'SOCIAL_PROOF': `
        🎯 SOCIAL PROOF:
        - TESTIMONIAL: Customer success story
        - STATISTICS: Numbers that build credibility
        - COMMUNITY: Show others like them
        - VALIDATION: Why they should trust you
      `
    };

    return frameworks[approach] || frameworks['AIDA'];
  }

  /**
   * Get word pool suggestions for creativity prompt
   */
  private static getWordPoolSuggestions(wordPool: any): string {
    if (!wordPool) return '';

    const suggestions: string[] = [];
    
    // Get sample words from each category
    Object.entries(wordPool).forEach(([category, words]: [string, any]) => {
      if (Array.isArray(words) && words.length > 0) {
        const sampleWords = words.slice(0, 3).join(', ');
        suggestions.push(`${category}: ${sampleWords}`);
      }
    });

    return suggestions.join(' | ');
  }
}

export default EnhancedCreativityPrompts;
