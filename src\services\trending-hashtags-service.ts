// src/services/trending-hashtags-service.ts
import { AdvancedTrendingHashtagAnalyzer } from '../ai/advanced-trending-hashtag-analyzer';
import type { AnalysisContext } from '../ai/advanced-trending-hashtag-analyzer';

interface TrendingHashtag {
  tag: string;
  trendScore: number; // 0-100
  momentum: 'rising' | 'stable' | 'falling';
  engagementRate: number; // average engagement rate for this hashtag
  postCount: number; // how many posts are using it recently
  relevanceScore: number; // how relevant to the business type
}

interface IndustryHashtagData {
  core: TrendingHashtag[];      // Always relevant hashtags for this industry
  trending: TrendingHashtag[];  // Currently trending hashtags
  seasonal: TrendingHashtag[];  // Season/time-based trending hashtags
  location?: TrendingHashtag[]; // Location-based hashtags if available
}

/**
 * Trending Hashtags Service
 * Provides industry-specific trending hashtags that seamlessly integrate into content generation
 */
export class TrendingHashtagsService {

  /**
   * Check if the context is a service rather than a business type
   */
  private static isServiceContext(context: string): boolean {
    const serviceKeywords = [
      'buy now pay later',
      'financing',
      'credit',
      'loan',
      'payment',
      'installment',
      'deferred payment',
      'flexible payment',
      'payment plan',
      'financial assistance',
      'funding',
      'investment',
      'banking',
      'financial services',
      'money management',
      'budgeting',
      'savings',
      'insurance',
      'mortgage',
      'refinancing'
    ];

    const contextLower = context.toLowerCase();
    return serviceKeywords.some(keyword => contextLower.includes(keyword));
  }

  /**
   * Get trending hashtags for a specific industry
   */
  static async getTrendingHashtags(
    businessType: string,
    location?: string,
    limit: number = 15
  ): Promise<string[]> {
    try {
      // Check if this is a service (contains specific service keywords) or business type
      const isService = this.isServiceContext(businessType);
      const contextType = isService ? 'service' : 'business type';

      console.log(`🔍 Analyzing ${contextType}: ${businessType}`);

      // First try to get real trending data from your RSS system
      const realTrendingHashtags = await this.fetchRealTrendingHashtags(businessType, location);

      if (realTrendingHashtags && realTrendingHashtags.length > 0) {
        console.log(`📈 Using real trending hashtags for ${businessType}:`, realTrendingHashtags.slice(0, 5));
        return realTrendingHashtags.slice(0, limit);
      }

      // Fallback to curated industry data when real data unavailable
      const industryData = isService
        ? await this.getServiceHashtagData(businessType, location)
        : await this.getIndustryHashtagData(businessType, location);

      // Combine and sort hashtags by relevance and trend score
      const allHashtags = [
        ...industryData.trending,
        ...industryData.core,
        ...industryData.seasonal,
        ...(industryData.location || [])
      ];

      // Sort by combined score (trend score + relevance score + engagement rate)
      const sortedHashtags = allHashtags
        .sort((a, b) => {
          const scoreA = (a.trendScore * 0.4) + (a.relevanceScore * 0.4) + (a.engagementRate * 0.2);
          const scoreB = (b.trendScore * 0.4) + (b.relevanceScore * 0.4) + (b.engagementRate * 0.2);
          return scoreB - scoreA;
        })
        .slice(0, limit);

      console.log(`📊 Using curated hashtags for ${businessType}:`, sortedHashtags.slice(0, 5).map(h => h.tag));
      return sortedHashtags.map(h => h.tag);

    } catch (error) {
      console.warn('Failed to fetch trending hashtags:', error);
      return this.getFallbackHashtags(businessType);
    }
  }

  /**
   * Fetch real trending hashtags using your advanced trending analyzer
   */
  private static async fetchRealTrendingHashtags(
    businessType: string,
    location?: string
  ): Promise<string[]> {
    try {
      // Use your existing Advanced Trending Hashtag Analyzer
      const analyzer = new AdvancedTrendingHashtagAnalyzer();

      const context: AnalysisContext = {
        businessType: businessType,
        businessName: '', // Can be passed in later
        location: location || 'USA',
        platform: 'instagram', // Default platform for analysis
        industry: businessType
      };

      console.log(`🔍 Using AdvancedTrendingHashtagAnalyzer for ${businessType}`);
      const strategy = await analyzer.analyzeHashtagTrends(context);

      if (strategy && strategy.finalRecommendations && strategy.finalRecommendations.length > 0) {
        console.log(`✅ Advanced analyzer returned ${strategy.finalRecommendations.length} hashtags`);
        return strategy.finalRecommendations;
      }

      // Fallback to RSS data endpoint if advanced analyzer doesn't work
      console.log('🔄 Falling back to direct RSS data endpoint');
      return await this.fetchDirectRSSHashtags(businessType);

    } catch (error) {
      console.warn('Advanced hashtag analyzer failed, trying direct RSS:', error);
      return await this.fetchDirectRSSHashtags(businessType);
    }
  }

  /**
   * Fallback hashtags when RSS import fails
   */
  private static getFallbackHashtags(businessType: string): string[] {
    const fallbackHashtags: Record<string, string[]> = {
      'technology': ['#TechNews', '#Innovation', '#Digital', '#AI', '#TechTrends'],
      'business': ['#Business', '#Entrepreneur', '#Startup', '#Success', '#Growth'],
      'finance': ['#Finance', '#Money', '#Investment', '#Banking', '#Fintech'],
      'food': ['#Food', '#Cooking', '#Recipe', '#Delicious', '#Foodie'],
      'health': ['#Health', '#Wellness', '#Fitness', '#Medical', '#Healthy'],
      'default': ['#Trending', '#Popular', '#News', '#Update', '#Latest']
    };

    const category = this.mapBusinessTypeToRSSCategory(businessType);
    return fallbackHashtags[category] || fallbackHashtags.default;
  }

  /**
   * Direct RSS data fetch as fallback (FIXED for 1K users)
   */
  private static async fetchDirectRSSHashtags(businessType: string): Promise<string[]> {
    try {
      console.log(`🔄 [Trending Hashtags] Using direct RSS fetch for ${businessType}`);

      // 🛡️ FIXED: Use direct RSS fetching instead of problematic API calls
      let fetchRSSFeedDirect;
      try {
        const rssModule = await import('../ai/utils/rss-direct-fetch');
        fetchRSSFeedDirect = rssModule.fetchRSSFeedDirect;
      } catch (importError) {
        console.warn('⚠️ [Trending Hashtags] RSS module import failed, using fallback:', importError);
        return this.getFallbackHashtags(businessType);
      }

      const category = this.mapBusinessTypeToRSSCategory(businessType);
      const articles = await fetchRSSFeedDirect(category, 30);

      if (articles && articles.length > 0) {
        // Extract hashtags from article keywords
        const allKeywords = articles.flatMap(article => article.keywords || []);
        const relevantHashtags = allKeywords
          .filter(keyword => this.isHashtagRelevantToBusiness(keyword, businessType))
          .map(keyword => `#${keyword.toLowerCase().replace(/\s+/g, '')}`)
          .filter(tag => tag.length > 2 && tag.length < 30); // Reasonable hashtag length

        // Remove duplicates and return top 10
        const uniqueHashtags = [...new Set(relevantHashtags)];
        console.log(`✅ [Trending Hashtags] Generated ${uniqueHashtags.length} hashtags from RSS data`);
        return uniqueHashtags.slice(0, 10);
      }

      console.warn(`⚠️ [Trending Hashtags] No articles found for ${category}, using fallback`);
      return this.getFallbackHashtags(businessType);

    } catch (error) {
      console.warn('❌ [Trending Hashtags] Direct RSS fetch failed:', error);
      return this.getFallbackHashtags(businessType);
    }
  }

  /**
   * Get fallback hashtags when all else fails
   */
  private static getFallbackHashtags(businessType: string): string[] {
    const fallbackHashtags = {
      'restaurant': ['#food', '#dining', '#restaurant', '#delicious', '#local', '#fresh'],
      'retail': ['#shopping', '#retail', '#fashion', '#deals', '#style', '#quality'],
      'tech': ['#technology', '#innovation', '#digital', '#tech', '#startup', '#software'],
      'healthcare': ['#health', '#wellness', '#care', '#medical', '#healthy', '#fitness'],
      'finance': ['#finance', '#money', '#investment', '#business', '#financial', '#success'],
      'education': ['#education', '#learning', '#knowledge', '#school', '#training', '#skills'],
      'default': ['#business', '#quality', '#service', '#professional', '#local', '#trusted']
    };

    const businessTypeLower = businessType.toLowerCase();
    for (const [key, hashtags] of Object.entries(fallbackHashtags)) {
      if (businessTypeLower.includes(key)) {
        return hashtags;
      }
    }

    return fallbackHashtags.default;
  }

  /**
   * Map business type to RSS category
   */
  private static mapBusinessTypeToRSSCategory(businessType: string): string {
    const categoryMap: Record<string, string> = {
      technology: 'tech',
      finance: 'business',
      retail: 'business',
      healthcare: 'general',
      restaurant: 'general',
      fitness: 'general'
    };

    return categoryMap[businessType.toLowerCase()] || 'general';
  }

  /**
   * Check if hashtag is relevant to business type
   */
  private static isHashtagRelevantToBusiness(hashtag: string, businessType: string): boolean {
    const businessKeywords: Record<string, string[]> = {
      restaurant: ['food', 'eat', 'chef', 'recipe', 'cook', 'dining', 'fresh', 'local', 'organic'],
      fitness: ['fitness', 'workout', 'gym', 'health', 'strong', 'training', 'exercise', 'wellness'],
      technology: ['tech', 'ai', 'digital', 'innovation', 'software', 'app', 'data', 'automation'],
      healthcare: ['health', 'medical', 'wellness', 'care', 'treatment', 'medicine', 'therapy'],
      retail: ['shopping', 'fashion', 'style', 'deals', 'sale', 'brand', 'quality', 'design']
    };

    const keywords = businessKeywords[businessType.toLowerCase()] || [];
    const hashtagLower = hashtag.toLowerCase().replace('#', '');

    return keywords.some(keyword =>
      hashtagLower.includes(keyword) || keyword.includes(hashtagLower)
    );
  }

  /**
   * Check if keyword is relevant to business type  
   */
  private static isKeywordRelevantToBusiness(keyword: string, businessType: string): boolean {
    return this.isHashtagRelevantToBusiness(`#${keyword}`, businessType);
  }

  /**
   * Get industry-specific hashtag data
   */
  private static async getIndustryHashtagData(
    businessType: string,
    location?: string
  ): Promise<IndustryHashtagData> {

    const industryHashtags: Record<string, IndustryHashtagData> = {
      restaurant: {
        core: [
          { tag: '#foodie', trendScore: 85, momentum: 'stable', engagementRate: 4.2, postCount: 45000, relevanceScore: 95 },
          { tag: '#delicious', trendScore: 82, momentum: 'stable', engagementRate: 3.8, postCount: 38000, relevanceScore: 90 },
          { tag: '#freshfood', trendScore: 78, momentum: 'rising', engagementRate: 4.1, postCount: 25000, relevanceScore: 92 },
        ],
        trending: [
          { tag: '#farmtotable', trendScore: 92, momentum: 'rising', engagementRate: 5.1, postCount: 12000, relevanceScore: 88 },
          { tag: '#sustainabledining', trendScore: 88, momentum: 'rising', engagementRate: 4.7, postCount: 8500, relevanceScore: 85 },
          { tag: '#locallysourced', trendScore: 86, momentum: 'rising', engagementRate: 4.5, postCount: 9200, relevanceScore: 89 },
          { tag: '#chefspecial', trendScore: 84, momentum: 'rising', engagementRate: 4.3, postCount: 7800, relevanceScore: 92 },
        ],
        seasonal: this.getSeasonalHashtags('restaurant'),
      },

      fitness: {
        core: [
          { tag: '#fitness', trendScore: 88, momentum: 'stable', engagementRate: 4.5, postCount: 125000, relevanceScore: 98 },
          { tag: '#workout', trendScore: 86, momentum: 'stable', engagementRate: 4.3, postCount: 98000, relevanceScore: 95 },
          { tag: '#healthylifestyle', trendScore: 84, momentum: 'stable', engagementRate: 4.1, postCount: 76000, relevanceScore: 93 },
        ],
        trending: [
          { tag: '#functionalfitness', trendScore: 94, momentum: 'rising', engagementRate: 5.2, postCount: 15000, relevanceScore: 91 },
          { tag: '#strengthtraining', trendScore: 90, momentum: 'rising', engagementRate: 4.8, postCount: 22000, relevanceScore: 89 },
          { tag: '#mindfulmovement', trendScore: 87, momentum: 'rising', engagementRate: 4.6, postCount: 11000, relevanceScore: 86 },
          { tag: '#transformationtuesday', trendScore: 85, momentum: 'stable', engagementRate: 5.0, postCount: 35000, relevanceScore: 88 },
        ],
        seasonal: this.getSeasonalHashtags('fitness'),
      },

      technology: {
        core: [
          { tag: '#tech', trendScore: 83, momentum: 'stable', engagementRate: 3.9, postCount: 156000, relevanceScore: 95 },
          { tag: '#innovation', trendScore: 81, momentum: 'stable', engagementRate: 3.7, postCount: 89000, relevanceScore: 93 },
          { tag: '#digitaltransformation', trendScore: 79, momentum: 'stable', engagementRate: 3.5, postCount: 45000, relevanceScore: 91 },
        ],
        trending: [
          { tag: '#aiproductivity', trendScore: 96, momentum: 'rising', engagementRate: 5.4, postCount: 8900, relevanceScore: 94 },
          { tag: '#automatedworkflow', trendScore: 91, momentum: 'rising', engagementRate: 4.9, postCount: 6700, relevanceScore: 89 },
          { tag: '#nocode', trendScore: 89, momentum: 'rising', engagementRate: 4.7, postCount: 12000, relevanceScore: 86 },
          { tag: '#techefficiency', trendScore: 87, momentum: 'rising', engagementRate: 4.4, postCount: 9300, relevanceScore: 88 },
        ],
        seasonal: this.getSeasonalHashtags('technology'),
      },

      healthcare: {
        core: [
          { tag: '#healthcare', trendScore: 85, momentum: 'stable', engagementRate: 4.0, postCount: 78000, relevanceScore: 98 },
          { tag: '#wellness', trendScore: 83, momentum: 'stable', engagementRate: 4.2, postCount: 67000, relevanceScore: 95 },
          { tag: '#health', trendScore: 81, momentum: 'stable', engagementRate: 3.8, postCount: 145000, relevanceScore: 93 },
        ],
        trending: [
          { tag: '#mentalhealthmatters', trendScore: 93, momentum: 'rising', engagementRate: 5.3, postCount: 18000, relevanceScore: 96 },
          { tag: '#preventivecare', trendScore: 88, momentum: 'rising', engagementRate: 4.6, postCount: 12000, relevanceScore: 92 },
          { tag: '#telemedicine', trendScore: 86, momentum: 'rising', engagementRate: 4.3, postCount: 9800, relevanceScore: 89 },
          { tag: '#wellnessjourney', trendScore: 84, momentum: 'stable', engagementRate: 4.7, postCount: 25000, relevanceScore: 91 },
        ],
        seasonal: this.getSeasonalHashtags('healthcare'),
      },

      retail: {
        core: [
          { tag: '#shopping', trendScore: 82, momentum: 'stable', engagementRate: 3.9, postCount: 234000, relevanceScore: 94 },
          { tag: '#fashion', trendScore: 80, momentum: 'stable', engagementRate: 4.1, postCount: 189000, relevanceScore: 88 },
          { tag: '#style', trendScore: 78, momentum: 'stable', engagementRate: 3.7, postCount: 167000, relevanceScore: 86 },
        ],
        trending: [
          { tag: '#sustainablefashion', trendScore: 91, momentum: 'rising', engagementRate: 4.9, postCount: 16000, relevanceScore: 90 },
          { tag: '#thrifting', trendScore: 89, momentum: 'rising', engagementRate: 5.1, postCount: 14000, relevanceScore: 87 },
          { tag: '#smallbusiness', trendScore: 87, momentum: 'rising', engagementRate: 4.6, postCount: 28000, relevanceScore: 93 },
          { tag: '#handmade', trendScore: 85, momentum: 'stable', engagementRate: 4.4, postCount: 32000, relevanceScore: 89 },
        ],
        seasonal: this.getSeasonalHashtags('retail'),
      }
    };

    const businessKey = businessType.toLowerCase();
    return industryHashtags[businessKey] || this.getGenericIndustryData();
  }

  /**
   * Get seasonal hashtags based on current date
   */
  private static getSeasonalHashtags(industry: string): TrendingHashtag[] {
    const currentMonth = new Date().getMonth();
    const isWinter = currentMonth >= 11 || currentMonth <= 2;
    const isSpring = currentMonth >= 2 && currentMonth <= 5;
    const isSummer = currentMonth >= 5 && currentMonth <= 8;
    const isFall = currentMonth >= 8 && currentMonth <= 11;

    const seasonalSets: Record<string, Record<string, TrendingHashtag[]>> = {
      restaurant: {
        winter: [
          { tag: '#comfortfood', trendScore: 89, momentum: 'rising', engagementRate: 4.8, postCount: 15000, relevanceScore: 92 },
          { tag: '#warmup', trendScore: 85, momentum: 'stable', engagementRate: 4.2, postCount: 8500, relevanceScore: 88 },
        ],
        spring: [
          { tag: '#freshstart', trendScore: 87, momentum: 'rising', engagementRate: 4.5, postCount: 12000, relevanceScore: 85 },
          { tag: '#springmenu', trendScore: 84, momentum: 'rising', engagementRate: 4.3, postCount: 7200, relevanceScore: 94 },
        ],
      },
      fitness: {
        winter: [
          { tag: '#newyeargoals', trendScore: 95, momentum: 'rising', engagementRate: 5.8, postCount: 25000, relevanceScore: 96 },
          { tag: '#2024transformation', trendScore: 91, momentum: 'rising', engagementRate: 5.2, postCount: 18000, relevanceScore: 89 },
        ],
        spring: [
          { tag: '#summerbodyready', trendScore: 92, momentum: 'rising', engagementRate: 5.4, postCount: 22000, relevanceScore: 93 },
          { tag: '#springworkout', trendScore: 86, momentum: 'rising', engagementRate: 4.7, postCount: 14000, relevanceScore: 87 },
        ],
      },
    };

    const season = isWinter ? 'winter' : isSpring ? 'spring' : isSummer ? 'summer' : 'fall';
    return seasonalSets[industry]?.[season] || [];
  }

  /**
   * Get service-specific hashtag data
   */
  private static async getServiceHashtagData(
    service: string,
    location?: string
  ): Promise<IndustryHashtagData> {
    const serviceLower = service.toLowerCase();

    // Service-specific hashtags
    const serviceHashtags: Record<string, IndustryHashtagData> = {
      'buy now pay later': {
        core: [
          { tag: '#BuyNowPayLater', trendScore: 88, momentum: 'rising', engagementRate: 4.5, postCount: 25000, relevanceScore: 95 },
          { tag: '#FlexiblePayment', trendScore: 85, momentum: 'rising', engagementRate: 4.2, postCount: 18000, relevanceScore: 92 },
          { tag: '#NoInterest', trendScore: 82, momentum: 'stable', engagementRate: 3.9, postCount: 15000, relevanceScore: 90 },
          { tag: '#EasyPayment', trendScore: 80, momentum: 'stable', engagementRate: 3.7, postCount: 12000, relevanceScore: 88 },
        ],
        trending: [
          { tag: '#PaymentPlans', trendScore: 92, momentum: 'rising', engagementRate: 5.1, postCount: 8000, relevanceScore: 94 },
          { tag: '#DeferredPayment', trendScore: 89, momentum: 'rising', engagementRate: 4.8, postCount: 6500, relevanceScore: 91 },
          { tag: '#InstallmentPlans', trendScore: 87, momentum: 'rising', engagementRate: 4.6, postCount: 7200, relevanceScore: 89 },
          { tag: '#FlexibleFinancing', trendScore: 85, momentum: 'rising', engagementRate: 4.4, postCount: 5800, relevanceScore: 87 },
        ],
        seasonal: this.getSeasonalHashtags('financial'),
      },
      'financing': {
        core: [
          { tag: '#Financing', trendScore: 86, momentum: 'stable', engagementRate: 4.1, postCount: 32000, relevanceScore: 93 },
          { tag: '#BusinessLoan', trendScore: 84, momentum: 'stable', engagementRate: 3.9, postCount: 28000, relevanceScore: 91 },
          { tag: '#QuickApproval', trendScore: 82, momentum: 'rising', engagementRate: 4.3, postCount: 15000, relevanceScore: 89 },
          { tag: '#LowInterest', trendScore: 80, momentum: 'stable', engagementRate: 3.8, postCount: 22000, relevanceScore: 87 },
        ],
        trending: [
          { tag: '#FastFunding', trendScore: 90, momentum: 'rising', engagementRate: 4.9, postCount: 12000, relevanceScore: 92 },
          { tag: '#BusinessGrowth', trendScore: 88, momentum: 'rising', engagementRate: 4.7, postCount: 18000, relevanceScore: 90 },
          { tag: '#WorkingCapital', trendScore: 86, momentum: 'rising', engagementRate: 4.5, postCount: 9500, relevanceScore: 88 },
        ],
        seasonal: this.getSeasonalHashtags('financial'),
      },
      'credit': {
        core: [
          { tag: '#Credit', trendScore: 87, momentum: 'stable', engagementRate: 4.2, postCount: 45000, relevanceScore: 94 },
          { tag: '#CreditScore', trendScore: 85, momentum: 'stable', engagementRate: 4.0, postCount: 38000, relevanceScore: 92 },
          { tag: '#CreditRepair', trendScore: 83, momentum: 'rising', engagementRate: 4.4, postCount: 15000, relevanceScore: 90 },
          { tag: '#CreditBuilding', trendScore: 81, momentum: 'rising', engagementRate: 4.1, postCount: 12000, relevanceScore: 88 },
        ],
        trending: [
          { tag: '#CreditEducation', trendScore: 91, momentum: 'rising', engagementRate: 5.0, postCount: 8000, relevanceScore: 93 },
          { tag: '#CreditTips', trendScore: 89, momentum: 'rising', engagementRate: 4.8, postCount: 11000, relevanceScore: 91 },
          { tag: '#FinancialLiteracy', trendScore: 87, momentum: 'rising', engagementRate: 4.6, postCount: 14000, relevanceScore: 89 },
        ],
        seasonal: this.getSeasonalHashtags('financial'),
      },
    };

    // Find matching service or use generic financial service data
    const matchingService = Object.keys(serviceHashtags).find(key =>
      serviceLower.includes(key.toLowerCase())
    );

    if (matchingService) {
      return serviceHashtags[matchingService];
    }

    // Default to generic financial service data
    return {
      core: [
        { tag: '#FinancialServices', trendScore: 85, momentum: 'stable', engagementRate: 4.0, postCount: 35000, relevanceScore: 90 },
        { tag: '#MoneyManagement', trendScore: 83, momentum: 'stable', engagementRate: 3.8, postCount: 28000, relevanceScore: 88 },
        { tag: '#FinancialSolutions', trendScore: 81, momentum: 'rising', engagementRate: 4.2, postCount: 18000, relevanceScore: 86 },
        { tag: '#FinancialAdvice', trendScore: 79, momentum: 'stable', engagementRate: 3.6, postCount: 22000, relevanceScore: 84 },
      ],
      trending: [
        { tag: '#FinancialFreedom', trendScore: 89, momentum: 'rising', engagementRate: 4.7, postCount: 15000, relevanceScore: 91 },
        { tag: '#SmartMoney', trendScore: 87, momentum: 'rising', engagementRate: 4.5, postCount: 12000, relevanceScore: 89 },
        { tag: '#FinancialPlanning', trendScore: 85, momentum: 'rising', engagementRate: 4.3, postCount: 16000, relevanceScore: 87 },
      ],
      seasonal: this.getSeasonalHashtags('financial'),
    };
  }

  /**
   * Generic industry data for unknown business types
   */
  private static getGenericIndustryData(): IndustryHashtagData {
    return {
      core: [
        { tag: '#business', trendScore: 75, momentum: 'stable', engagementRate: 3.2, postCount: 89000, relevanceScore: 80 },
        { tag: '#entrepreneur', trendScore: 73, momentum: 'stable', engagementRate: 3.4, postCount: 67000, relevanceScore: 82 },
        { tag: '#success', trendScore: 71, momentum: 'stable', engagementRate: 3.1, postCount: 78000, relevanceScore: 78 },
      ],
      trending: [
        { tag: '#innovation', trendScore: 83, momentum: 'rising', engagementRate: 4.1, postCount: 15000, relevanceScore: 85 },
        { tag: '#growth', trendScore: 81, momentum: 'rising', engagementRate: 3.9, postCount: 12000, relevanceScore: 82 },
        { tag: '#leadership', trendScore: 79, momentum: 'rising', engagementRate: 3.7, postCount: 9500, relevanceScore: 80 },
      ],
      seasonal: [],
    };
  }

  /**
   * Fallback hashtags when service fails
   */
  private static getFallbackHashtags(businessType: string): string[] {
    const fallbacks: Record<string, string[]> = {
      restaurant: ['#foodie', '#delicious', '#freshfood', '#localeats', '#yummy'],
      fitness: ['#fitness', '#workout', '#healthylifestyle', '#motivation', '#strong'],
      technology: ['#tech', '#innovation', '#digital', '#future', '#ai'],
      healthcare: ['#health', '#wellness', '#care', '#medical', '#healthy'],
      retail: ['#shopping', '#fashion', '#style', '#deals', '#quality'],
    };

    return fallbacks[businessType.toLowerCase()] || ['#business', '#quality', '#service', '#professional', '#growth'];
  }

  /**
   * Get hashtags optimized for specific platform
   */
  static getplatformOptimizedHashtags(hashtags: string[], platform: string): string[] {
    // Different platforms have different hashtag strategies
    const platformLimits: Record<string, number> = {
      instagram: 5,  // Instagram performs well with 5 hashtags
      twitter: 3,    // Twitter works best with 3 hashtags
      linkedin: 3,   // LinkedIn optimal is 3 hashtags
      facebook: 3,   // Facebook works with 3 hashtags
    };

    const limit = platformLimits[platform.toLowerCase()] || 3;
    return hashtags.slice(0, limit);
  }
}
