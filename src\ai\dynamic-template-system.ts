/**
 * Dynamic Template System - Replaces static templates with AI-generated dynamic variations
 * Creates unique, context-aware templates that adapt to business and location specifics
 */

export interface TemplateConfig {
  businessName: string;
  businessType: string;
  location: string;
  businessDetails: any;
  context: any;
  creativityLevel: number;
  platform: string;
}

export interface DynamicTemplate {
  id: string;
  type: 'headline' | 'subheadline' | 'caption' | 'cta';
  template: string;
  variables: string[];
  context: string;
  adaptability: number;
  uniqueness: number;
  businessRelevance: number;
  generatedAt: number;
}

export interface TemplateVariation {
  template: DynamicTemplate;
  variation: string;
  score: number;
  reasoning: string;
  strengths: string[];
  improvements: string[];
}

export interface TemplateGenerationResult {
  bestTemplate: DynamicTemplate;
  variations: TemplateVariation[];
  recommendations: string[];
  insights: {
    templateQuality: number;
    uniquenessScore: number;
    adaptabilityScore: number;
    businessRelevanceScore: number;
  };
}

export class DynamicTemplateSystem {
  private templateCache: Map<string, DynamicTemplate[]> = new Map();
  private templateUsage: Map<string, number> = new Map();
  private templatePerformance: Map<string, number> = new Map();
  private readonly CACHE_DURATION = 60 * 60 * 1000; // 1 hour

  /**
   * Generate dynamic templates for content creation
   */
  async generateDynamicTemplates(config: TemplateConfig): Promise<TemplateGenerationResult> {
    const { businessName, businessType, location, businessDetails, context, creativityLevel, platform } = config;

    try {
      // Generate templates for each content type
      const [headlineTemplates, subheadlineTemplates, captionTemplates, ctaTemplates] = await Promise.all([
        this.generateTemplatesForType('headline', config),
        this.generateTemplatesForType('subheadline', config),
        this.generateTemplatesForType('caption', config),
        this.generateTemplatesForType('cta', config)
      ]);

      // Select best template for each type
      const bestHeadline = this.selectBestTemplate(headlineTemplates, config);
      const bestSubheadline = this.selectBestTemplate(subheadlineTemplates, config);
      const bestCaption = this.selectBestTemplate(captionTemplates, config);
      const bestCta = this.selectBestTemplate(ctaTemplates, config);

      // Generate variations for the best templates
      const variations = await this.generateTemplateVariations([bestHeadline, bestSubheadline, bestCaption, bestCta], config);

      // Calculate insights
      const insights = this.calculateTemplateInsights([bestHeadline, bestSubheadline, bestCaption, bestCta]);

      // Generate recommendations
      const recommendations = this.generateTemplateRecommendations(insights, config);

      return {
        bestTemplate: bestHeadline, // Return headline as primary template
        variations,
        recommendations,
        insights
      };
    } catch (error) {
      console.error('Dynamic template generation failed:', error);
      return this.generateFallbackTemplates(config);
    }
  }

  /**
   * Generate templates for a specific content type
   */
  private async generateTemplatesForType(type: 'headline' | 'subheadline' | 'caption' | 'cta', config: TemplateConfig): Promise<DynamicTemplate[]> {
    const { businessName, businessType, location, businessDetails, context, creativityLevel, platform } = config;

    try {
      const prompt = this.buildTemplatePrompt(type, config);
      const response = await this.callAI(prompt);
      const templates = this.parseTemplateResponse(response, type);
      
      // Cache templates
      const cacheKey = `${type}_${businessType}_${location}`;
      this.templateCache.set(cacheKey, templates);
      
      return templates;
    } catch (error) {
      console.error(`Failed to generate ${type} templates:`, error);
      return this.getFallbackTemplates(type, config);
    }
  }

  /**
   * Build template generation prompt
   */
  private buildTemplatePrompt(type: string, config: TemplateConfig): string {
    const { businessName, businessType, location, businessDetails, context, creativityLevel, platform } = config;

    const typeInstructions = this.getTypeInstructions(type);
    
    return `
🎯 DYNAMIC TEMPLATE GENERATION - ${type.toUpperCase()}

You are an expert template designer creating dynamic, adaptable templates for ${businessName}, a ${businessType} in ${location}.

TEMPLATE REQUIREMENTS:
${typeInstructions}

BUSINESS CONTEXT:
- Business: ${businessName}
- Type: ${businessType}
- Location: ${location}
- Platform: ${platform}
- Creativity Level: ${creativityLevel}/100

CONTEXT DATA:
${JSON.stringify(context).slice(0, 500)}...

TEMPLATE SPECIFICATIONS:
- Create 5 unique, dynamic templates
- Each template must be adaptable to different contexts
- Use variables like {businessName}, {location}, {businessType}, {service}, {benefit}
- Templates should be platform-optimized for ${platform}
- Avoid repetitive patterns or generic language
- Make templates specific to ${businessType} industry
- Include psychological triggers appropriate for ${businessType}

OUTPUT FORMAT:
{
  "templates": [
    {
      "id": "template_1",
      "template": "Template text with {variables}",
      "variables": ["variable1", "variable2"],
      "context": "When to use this template",
      "adaptability": 0.8,
      "uniqueness": 0.9,
      "businessRelevance": 0.85
    }
  ]
}

Generate templates that are:
- Highly adaptable to different business contexts
- Unique and non-repetitive
- Relevant to ${businessType} industry
- Optimized for ${platform} platform
- Creative and engaging
`;
  }

  /**
   * Get type-specific instructions
   */
  private getTypeInstructions(type: string): string {
    const instructions: Record<string, string> = {
      'headline': `
        HEADLINE TEMPLATES:
        - 5-8 words maximum
        - Attention-grabbing and compelling
        - Use power words and emotional triggers
        - Include business name or location when relevant
        - Create urgency or curiosity
        - Avoid generic phrases
      `,
      'subheadline': `
        SUBHEADLINE TEMPLATES:
        - 10-20 words maximum
        - Support and expand on headline
        - Include specific benefits or features
        - Use local references when appropriate
        - Create desire or interest
        - Be specific to business type
      `,
      'caption': `
        CAPTION TEMPLATES:
        - 50-200 words maximum
        - Tell a story or provide value
        - Include call-to-action
        - Use local language and cultural references
        - Include relevant hashtags
        - Be engaging and shareable
      `,
      'cta': `
        CTA TEMPLATES:
        - 3-8 words maximum
        - Action-oriented and clear
        - Create urgency or excitement
        - Use strong verbs
        - Be specific to business offering
        - Avoid generic phrases
      `
    };

    return instructions[type] || instructions['headline'];
  }

  /**
   * Parse template response
   */
  private parseTemplateResponse(response: string, type: string): DynamicTemplate[] {
    try {
      const data = JSON.parse(response);
      return data.templates.map((template: any, index: number) => ({
        id: template.id || `template_${type}_${index + 1}`,
        type: type as DynamicTemplate['type'],
        template: template.template,
        variables: template.variables || [],
        context: template.context || '',
        adaptability: template.adaptability || 0.5,
        uniqueness: template.uniqueness || 0.5,
        businessRelevance: template.businessRelevance || 0.5,
        generatedAt: Date.now()
      }));
    } catch (error) {
      console.error(`Failed to parse ${type} templates:`, error);
      return this.getFallbackTemplates(type, {} as TemplateConfig);
    }
  }

  /**
   * Select best template from available options
   */
  private selectBestTemplate(templates: DynamicTemplate[], config: TemplateConfig): DynamicTemplate {
    if (templates.length === 0) {
      return this.getFallbackTemplate(config);
    }

    // Score templates based on multiple factors
    const scoredTemplates = templates.map(template => ({
      template,
      score: this.calculateTemplateScore(template, config)
    }));

    // Sort by score and return best
    scoredTemplates.sort((a, b) => b.score - a.score);
    return scoredTemplates[0].template;
  }

  /**
   * Calculate template score
   */
  private calculateTemplateScore(template: DynamicTemplate, config: TemplateConfig): number {
    let score = 0;

    // Base scores from template properties
    score += template.adaptability * 30;
    score += template.uniqueness * 25;
    score += template.businessRelevance * 25;

    // Usage frequency penalty
    const usageCount = this.templateUsage.get(template.id) || 0;
    score -= usageCount * 5;

    // Performance bonus
    const performance = this.templatePerformance.get(template.id) || 0.5;
    score += performance * 20;

    // Variable richness bonus
    score += Math.min(template.variables.length * 2, 10);

    // Context relevance bonus
    if (template.context.includes(config.businessType)) score += 5;
    if (template.context.includes(config.location)) score += 5;

    return Math.max(score, 0);
  }

  /**
   * Generate template variations
   */
  private async generateTemplateVariations(templates: DynamicTemplate[], config: TemplateConfig): Promise<TemplateVariation[]> {
    const variations: TemplateVariation[] = [];

    for (const template of templates) {
      try {
        const variation = await this.generateTemplateVariation(template, config);
        variations.push(variation);
      } catch (error) {
        console.error(`Failed to generate variation for ${template.id}:`, error);
      }
    }

    return variations;
  }

  /**
   * Generate single template variation
   */
  private async generateTemplateVariation(template: DynamicTemplate, config: TemplateConfig): Promise<TemplateVariation> {
    const { businessName, businessType, location, businessDetails } = config;

    // Replace variables with actual values
    let variation = template.template;
    template.variables.forEach(variable => {
      const value = this.getVariableValue(variable, config);
      variation = variation.replace(`{${variable}}`, value);
    });

    // Calculate variation score
    const score = this.calculateVariationScore(variation, template, config);

    // Generate reasoning
    const reasoning = this.generateVariationReasoning(template, variation, config);

    // Identify strengths and improvements
    const strengths = this.identifyVariationStrengths(variation, config);
    const improvements = this.identifyVariationImprovements(variation, config);

    return {
      template,
      variation,
      score,
      reasoning,
      strengths,
      improvements
    };
  }

  /**
   * Get variable value
   */
  private getVariableValue(variable: string, config: TemplateConfig): string {
    const { businessName, businessType, location, businessDetails } = config;

    const variableMap: Record<string, string> = {
      'businessName': businessName,
      'businessType': businessType,
      'location': location,
      'service': businessDetails.services || 'service',
      'benefit': this.getRandomBenefit(businessType),
      'feature': this.getRandomFeature(businessType),
      'emotion': this.getRandomEmotion(),
      'action': this.getRandomAction(),
      'quality': this.getRandomQuality(),
      'time': this.getRandomTimeReference()
    };

    return variableMap[variable] || variable;
  }

  /**
   * Calculate variation score
   */
  private calculateVariationScore(variation: string, template: DynamicTemplate, config: TemplateConfig): number {
    let score = 0;

    // Length appropriateness
    const length = variation.length;
    if (template.type === 'headline' && length >= 5 && length <= 15) score += 20;
    if (template.type === 'subheadline' && length >= 10 && length <= 25) score += 20;
    if (template.type === 'caption' && length >= 50 && length <= 200) score += 20;
    if (template.type === 'cta' && length >= 3 && length <= 8) score += 20;

    // Business relevance
    if (variation.toLowerCase().includes(config.businessName.toLowerCase())) score += 15;
    if (variation.toLowerCase().includes(config.businessType.toLowerCase())) score += 10;
    if (variation.toLowerCase().includes(config.location.toLowerCase())) score += 10;

    // Uniqueness
    const words = variation.toLowerCase().split(/\s+/);
    const uniqueWords = new Set(words);
    const uniquenessRatio = uniqueWords.size / words.length;
    score += uniquenessRatio * 15;

    // Emotional impact
    const emotionalWords = ['amazing', 'incredible', 'outstanding', 'excellent', 'fantastic', 'wonderful', 'great', 'awesome'];
    const emotionalCount = emotionalWords.filter(word => variation.toLowerCase().includes(word)).length;
    score += Math.min(emotionalCount * 5, 15);

    return Math.min(score, 100);
  }

  /**
   * Generate variation reasoning
   */
  private generateVariationReasoning(template: DynamicTemplate, variation: string, config: TemplateConfig): string {
    const reasons: string[] = [];

    if (variation.includes(config.businessName)) {
      reasons.push('Personalized with business name');
    }
    if (variation.includes(config.location)) {
      reasons.push('Includes local reference');
    }
    if (template.variables.length > 0) {
      reasons.push('Uses dynamic variables effectively');
    }
    if (variation.length > 10) {
      reasons.push('Appropriate length for content type');
    }

    return reasons.join(', ') || 'Standard template application';
  }

  /**
   * Identify variation strengths
   */
  private identifyVariationStrengths(variation: string, config: TemplateConfig): string[] {
    const strengths: string[] = [];

    if (variation.includes(config.businessName)) {
      strengths.push('Personalized');
    }
    if (variation.includes(config.location)) {
      strengths.push('Localized');
    }
    if (variation.length > 20) {
      strengths.push('Detailed');
    }
    if (variation.includes('!') || variation.includes('?')) {
      strengths.push('Engaging');
    }

    return strengths;
  }

  /**
   * Identify variation improvements
   */
  private identifyVariationImprovements(variation: string, config: TemplateConfig): string[] {
    const improvements: string[] = [];

    if (!variation.includes(config.businessName)) {
      improvements.push('Add business name for personalization');
    }
    if (!variation.includes(config.location)) {
      improvements.push('Include location reference');
    }
    if (variation.length < 10) {
      improvements.push('Add more detail');
    }
    if (!variation.includes('!') && !variation.includes('?')) {
      improvements.push('Add engaging punctuation');
    }

    return improvements;
  }

  /**
   * Calculate template insights
   */
  private calculateTemplateInsights(templates: DynamicTemplate[]): TemplateGenerationResult['insights'] {
    const totalTemplates = templates.length;
    
    const avgAdaptability = templates.reduce((sum, t) => sum + t.adaptability, 0) / totalTemplates;
    const avgUniqueness = templates.reduce((sum, t) => sum + t.uniqueness, 0) / totalTemplates;
    const avgBusinessRelevance = templates.reduce((sum, t) => sum + t.businessRelevance, 0) / totalTemplates;
    
    const templateQuality = (avgAdaptability + avgUniqueness + avgBusinessRelevance) / 3;

    return {
      templateQuality,
      uniquenessScore: avgUniqueness,
      adaptabilityScore: avgAdaptability,
      businessRelevanceScore: avgBusinessRelevance
    };
  }

  /**
   * Generate template recommendations
   */
  private generateTemplateRecommendations(insights: TemplateGenerationResult['insights'], config: TemplateConfig): string[] {
    const recommendations: string[] = [];

    if (insights.templateQuality < 0.7) {
      recommendations.push('Improve template quality by increasing adaptability and uniqueness');
    }
    if (insights.uniquenessScore < 0.8) {
      recommendations.push('Focus on creating more unique templates to avoid repetition');
    }
    if (insights.adaptabilityScore < 0.8) {
      recommendations.push('Increase template adaptability for different contexts');
    }
    if (insights.businessRelevanceScore < 0.8) {
      recommendations.push('Make templates more relevant to the specific business type');
    }

    recommendations.push('Use dynamic variables to create personalized content');
    recommendations.push('Regularly update templates to maintain freshness');

    return recommendations;
  }

  /**
   * Helper methods for variable values
   */
  private getRandomBenefit(businessType: string): string {
    const benefits: Record<string, string[]> = {
      'restaurant': ['delicious food', 'great service', 'amazing taste', 'fresh ingredients'],
      'bakery': ['fresh bread', 'sweet treats', 'artisan quality', 'homemade goodness'],
      'fitness': ['stronger body', 'better health', 'increased energy', 'fitness goals'],
      'beauty': ['beautiful skin', 'confidence boost', 'radiant glow', 'stunning look'],
      'retail': ['great deals', 'quality products', 'excellent value', 'amazing selection'],
      'tech': ['innovative solutions', 'better performance', 'efficient systems', 'cutting-edge technology'],
      'service': ['expert service', 'professional results', 'reliable support', 'quality assistance']
    };

    const typeBenefits = benefits[businessType.toLowerCase()] || benefits['service'];
    return typeBenefits[Math.floor(Math.random() * typeBenefits.length)];
  }

  private getRandomFeature(businessType: string): string {
    const features: Record<string, string[]> = {
      'restaurant': ['fresh ingredients', 'local sourcing', 'chef expertise', 'cozy atmosphere'],
      'bakery': ['handcrafted', 'oven-fresh', 'artisan techniques', 'premium quality'],
      'fitness': ['modern equipment', 'expert trainers', 'flexible schedules', 'personalized programs'],
      'beauty': ['professional techniques', 'quality products', 'personalized service', 'expert advice'],
      'retail': ['wide selection', 'competitive prices', 'quality products', 'excellent service'],
      'tech': ['latest technology', 'expert support', 'reliable systems', 'innovative solutions'],
      'service': ['experienced team', 'proven methods', 'reliable results', 'professional approach']
    };

    const typeFeatures = features[businessType.toLowerCase()] || features['service'];
    return typeFeatures[Math.floor(Math.random() * typeFeatures.length)];
  }

  private getRandomEmotion(): string {
    const emotions = ['excitement', 'joy', 'satisfaction', 'confidence', 'happiness', 'pride'];
    return emotions[Math.floor(Math.random() * emotions.length)];
  }

  private getRandomAction(): string {
    const actions = ['discover', 'experience', 'enjoy', 'achieve', 'transform', 'explore'];
    return actions[Math.floor(Math.random() * actions.length)];
  }

  private getRandomQuality(): string {
    const qualities = ['excellent', 'outstanding', 'premium', 'superior', 'exceptional', 'remarkable'];
    return qualities[Math.floor(Math.random() * qualities.length)];
  }

  private getRandomTimeReference(): string {
    const timeRefs = ['today', 'now', 'this week', 'immediately', 'right away', 'soon'];
    return timeRefs[Math.floor(Math.random() * timeRefs.length)];
  }

  /**
   * Simulate AI call
   */
  private async callAI(prompt: string): Promise<string> {
    // This would be replaced with actual AI service call
    return JSON.stringify({
      templates: [
        {
          id: 'template_1',
          template: 'Transform your {businessType} experience with {businessName}',
          variables: ['businessType', 'businessName'],
          context: 'When highlighting transformation',
          adaptability: 0.9,
          uniqueness: 0.8,
          businessRelevance: 0.85
        }
      ]
    });
  }

  /**
   * Fallback methods
   */
  private getFallbackTemplates(type: string, config: TemplateConfig): DynamicTemplate[] {
    return [this.getFallbackTemplate(config)];
  }

  private getFallbackTemplate(config: TemplateConfig): DynamicTemplate {
    return {
      id: 'fallback_template',
      type: 'headline',
      template: `Quality {businessType} services in {location}`,
      variables: ['businessType', 'location'],
      context: 'Fallback template',
      adaptability: 0.5,
      uniqueness: 0.3,
      businessRelevance: 0.6,
      generatedAt: Date.now()
    };
  }

  private generateFallbackTemplates(config: TemplateConfig): TemplateGenerationResult {
    const fallbackTemplate = this.getFallbackTemplate(config);
    
    return {
      bestTemplate: fallbackTemplate,
      variations: [],
      recommendations: ['Use fallback templates', 'Retry generation with different parameters'],
      insights: {
        templateQuality: 0.5,
        uniquenessScore: 0.3,
        adaptabilityScore: 0.5,
        businessRelevanceScore: 0.6
      }
    };
  }
}

// Export singleton instance
export const dynamicTemplateSystem = new DynamicTemplateSystem();

export default DynamicTemplateSystem;

